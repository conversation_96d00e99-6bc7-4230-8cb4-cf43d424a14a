/* IPODashboard.css - Bluestock IPO Management */
.ipo-dashboard {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

/* Sidebar */
.dashboard-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-info h3 {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.profile-info p {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.profile-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.notification-btn,
.settings-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.notification-btn:hover,
.settings-btn:hover {
  background: #f3f4f6;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-section h4 {
  font-size: 11px;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 12px 20px;
}

.nav-section ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-section li {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #6b7280;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: #f3f4f6;
  color: #374151;
}

.nav-link.active {
  background: #eff6ff;
  color: #2563eb;
  border-right: 3px solid #2563eb;
}

/* Main Content */
.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.header-left p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-outline {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #2563eb;
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
}

.btn-text {
  background: none;
  color: #6b7280;
  padding: 8px 12px;
}

.btn-text:hover {
  color: #374151;
  background: #f3f4f6;
}

/* Content Tabs */
.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  padding: 0 32px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  color: #374151;
}

.tab-btn.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

.tab-content {
  flex: 1;
  padding: 32px;
}

/* IPO Form */
.ipo-form-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.ipo-form-section p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 32px 0;
}

.ipo-form {
  max-width: 800px;
}

.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input::placeholder {
  color: #9ca3af;
}

/* Logo Upload */
.logo-upload {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #f9fafb;
}

.logo-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-placeholder {
  width: 48px;
  height: 48px;
  background: #e5e7eb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.logo-preview span {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.logo-preview small {
  font-size: 12px;
  color: #6b7280;
}

.upload-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

/* Form Section */
.form-section {
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #e5e7eb;
}

.form-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 24px 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-sidebar {
    width: 240px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .ipo-dashboard {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    width: 100%;
    height: auto;
  }
  
  .main-header {
    padding: 16px 20px;
  }
  
  .tab-content {
    padding: 20px;
  }
}
