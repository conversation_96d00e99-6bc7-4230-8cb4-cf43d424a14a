/* SignIn.css - Bluestock Design */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 400px;
  text-align: center;
  border: 1px solid #e9ecef;
}



.auth-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  gap: 8px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  position: relative;
}

.logo-bars {
  display: flex;
  gap: 2px;
  height: 100%;
  align-items: flex-end;
}

.bar {
  width: 6px;
  background: #6366f1;
  border-radius: 1px;
}

.bar-1 {
  height: 60%;
}

.bar-2 {
  height: 100%;
}

.bar-3 {
  height: 80%;
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: 0.5px;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 30px;
  text-align: left;
}

.auth-form {
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  background: #fff;
}

.form-group input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input::placeholder {
  color: #9ca3af;
}

.forgot-password {
  display: block;
  margin-top: 6px;
  color: #6366f1;
  text-decoration: none;
  font-size: 13px;
  float: right;
}

.forgot-password:hover {
  text-decoration: underline;
}

.form-options {
  margin-bottom: 25px;
  text-align: left;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
  color: #6b7280;
}

.checkbox-container input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #6366f1;
}

.auth-btn {
  width: 100%;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.primary-btn {
  background: #6366f1;
  color: white;
  margin-bottom: 20px;
}

.primary-btn:hover {
  background: #5856eb;
}

.google-btn {
  background: #f8f9fa;
  color: #374151;
  border: 1px solid #d1d5db;
}

.google-btn:hover {
  background: #f1f3f4;
  border-color: #9ca3af;
}

.google-btn img {
  width: 18px;
  height: 18px;
}

.auth-divider {
  margin: 20px 0;
  position: relative;
  text-align: center;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.auth-divider span {
  background: white;
  padding: 0 15px;
  color: #6b7280;
  font-size: 13px;
}

.auth-footer {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.create-account-link {
  color: #6366f1;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.create-account-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-container {
    padding: 10px;
  }

  .auth-card {
    padding: 30px 20px;
  }

  .logo-text {
    font-size: 16px;
  }

  .auth-title {
    font-size: 20px;
  }
}