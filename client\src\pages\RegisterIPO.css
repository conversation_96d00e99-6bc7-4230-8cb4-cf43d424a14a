.register-ipo-page {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 0;
}

.page-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-cancel {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-register {
  padding: 10px 20px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-register:hover {
  background: #1d4ed8;
}

.content-wrapper {
  padding: 32px;
}

.form-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  padding: 32px 32px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.form-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.form-header p {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.form-tabs {
  display: flex;
  border-bottom: 1px solid #f3f4f6;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

.tab-number {
  width: 24px;
  height: 24px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.tab-btn.active .tab-number {
  background: #2563eb;
  color: white;
}

.ipo-form {
  padding: 32px;
}

.form-section {
  margin-bottom: 40px;
}

.form-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.form-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 32px 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #1f2937;
  background: white;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input::placeholder {
  color: #9ca3af;
}

.logo-upload {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
}

.logo-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-placeholder {
  width: 48px;
  height: 48px;
  background: #ff6b35;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.logo-info {
  display: flex;
  flex-direction: column;
}

.company-tag {
  background: #fef3c7;
  color: #92400e;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
  width: fit-content;
}

.company-type {
  font-size: 12px;
  color: #6b7280;
}

.upload-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.btn-upload,
.btn-delete {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-upload:hover,
.btn-delete:hover {
  background: #f9fafb;
}

.btn-delete {
  color: #dc2626;
  border-color: #fecaca;
}

.btn-delete:hover {
  background: #fef2f2;
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px 20px;
  }
  
  .ipo-form {
    padding: 20px;
  }
}
