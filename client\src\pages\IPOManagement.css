/* IPO Management Page Styles */
.ipo-management {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.ipo-management .container-fluid {
  padding: 0;
}

/* Sidebar Styles */
.sidebar {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 0;
  border-right: 1px solid #e9ecef;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.sidebar-header h5 {
  color: #6c757d;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 0;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #495057;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
  text-decoration: none;
}

.nav-item.active {
  background-color: #e3f2fd;
  color: #1976d2;
  border-right: 3px solid #1976d2;
}

.nav-item i {
  margin-right: 12px;
  width: 16px;
}

.sidebar-section {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
  margin-top: 20px;
}

.sidebar-section h6 {
  color: #6c757d;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 0 0 15px 20px;
}

/* Main Content */
.main-content {
  padding: 30px;
  background-color: #f8f9fa;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.header-left h2 {
  font-size: 28px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 5px 0;
}

.header-left p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 24px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background-color: white;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-btn {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 6px;
  color: #6c757d;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  background-color: #007bff;
  color: white;
}

.tab-btn i {
  margin-right: 8px;
}

/* IPO Form */
.ipo-form {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.form-header {
  margin-bottom: 30px;
}

.form-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 5px 0;
}

.form-header p {
  color: #6c757d;
  margin: 0;
  font-size: 14px;
}

/* Logo Section */
.logo-section {
  margin-bottom: 30px;
}

.logo-section label {
  display: block;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  font-size: 14px;
}

.logo-upload {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-preview {
  width: 80px;
  height: 80px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  text-align: center;
  color: #6c757d;
}

.logo-icon {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.logo-placeholder small {
  font-size: 10px;
}

.logo-actions {
  display: flex;
  gap: 8px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-control {
  padding: 12px 16px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-control::placeholder {
  color: #adb5bd;
}

/* Listed Section */
.listed-section {
  border-top: 1px solid #e9ecef;
  padding-top: 30px;
  margin-top: 30px;
}

.listed-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Auth Required */
.auth-required {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px 20px;
}

.auth-required h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.auth-required p {
  color: #6c757d;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 20px 15px;
  }
  
  .content-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .logo-upload {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .tab-navigation {
    flex-direction: column;
  }
  
  .sidebar {
    display: none;
  }
}
