import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Home.css';

const Home = () => {
  const { user } = useAuth();

  return (
    <div className="home-page">
      <div className="hero-section">
        <div className="hero-container">
          <div className="hero-content">
            <h1>Welcome to Bluestock</h1>
            <p>Your trusted partner for IPO investments and financial growth</p>
            {!user && (
              <div className="hero-actions">
                <Link to="/signup" className="btn btn-primary">
                  Get Started
                </Link>
                <Link to="/signin" className="btn btn-outline">
                  Sign In
                </Link>
              </div>
            )}
            {user && (
              <div className="hero-actions">
                <Link to="/admin-dashboard" className="btn btn-primary">
                  Admin Dashboard
                </Link>
                <Link to="/ipo-table" className="btn btn-outline">
                  IPO Management
                </Link>
              </div>
            )}
          </div>
          <div className="hero-image">
            <div className="hero-graphic">
              <div className="chart-bars">
                <div className="chart-bar" style={{height: '60%'}}></div>
                <div className="chart-bar" style={{height: '80%'}}></div>
                <div className="chart-bar" style={{height: '100%'}}></div>
                <div className="chart-bar" style={{height: '70%'}}></div>
                <div className="chart-bar" style={{height: '90%'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="features-section">
        <div className="features-container">
          <h2>Why Choose Bluestock?</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">📈</div>
              <h3>IPO Management</h3>
              <p>Comprehensive IPO tracking and management tools for investors</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🔒</div>
              <h3>Secure Platform</h3>
              <p>Bank-grade security with Google OAuth integration</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">📊</div>
              <h3>Real-time Data</h3>
              <p>Live market data and IPO updates at your fingertips</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🎯</div>
              <h3>Expert Analysis</h3>
              <p>Professional insights and recommendations for better decisions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
