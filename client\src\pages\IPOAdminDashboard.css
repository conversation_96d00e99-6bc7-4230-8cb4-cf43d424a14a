/* IPO Admin Dashboard Styles */
.ipo-admin-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: white;
}

.ipo-admin-dashboard .main-content {
  flex: 1;
  margin-left: 280px;
  padding: 0;
}

.ipo-admin-dashboard .container-fluid {
  padding: 0;
}

/* Sidebar Styles */
.ipo-admin-dashboard .sidebar {
  background-color: #2d2d2d;
  min-height: 100vh;
  padding: 0;
  border-right: 1px solid #404040;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #404040;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-icon {
  font-size: 24px;
}

.brand-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-section {
  margin-bottom: 30px;
}

.nav-section h6 {
  color: #888;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 0 0 15px 20px;
  text-transform: uppercase;
}

.ipo-admin-dashboard .nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #ccc;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.ipo-admin-dashboard .nav-item:hover {
  background-color: #404040;
  color: white;
  text-decoration: none;
}

.ipo-admin-dashboard .nav-item.active {
  background-color: #007bff;
  color: white;
}

.ipo-admin-dashboard .nav-item i {
  margin-right: 12px;
  width: 16px;
}

/* Main Content */
.ipo-admin-dashboard .main-content {
  padding: 30px;
  background-color: #1a1a1a;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info span {
  color: #ccc;
  font-size: 14px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Info Cards */
.info-card {
  background-color: #2d2d2d;
  border-radius: 12px;
  padding: 24px;
  height: 100%;
  border: 1px solid #404040;
}

.info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0 0 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.view-report-btn {
  background-color: #6c5ce7;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}

/* Chart Styles */
.chart-container {
  position: relative;
  margin-bottom: 20px;
}

.donut-chart {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.chart-number {
  display: block;
  font-size: 36px;
  font-weight: 700;
  color: white;
}

.chart-label {
  display: block;
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #ccc;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.active {
  background-color: #007bff;
}

.legend-color.upcoming {
  background-color: #28a745;
}

.info-subtitle {
  color: #888;
  font-size: 12px;
  margin: 0;
  text-align: center;
}

/* Quick Links */
.quick-links {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.quick-link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #404040;
}

.quick-link-item:last-child {
  border-bottom: none;
}

.link-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.link-icon {
  font-size: 20px;
}

.link-name {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.link-action {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* Board Chart */
.board-chart {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.percentage-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.percentage {
  font-size: 48px;
  font-weight: 700;
  color: white;
}

.percentage-label {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.chart-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.stat-label {
  font-size: 10px;
  color: #888;
  margin-top: 2px;
}

/* Activity Card */
.activity-card {
  background-color: #2d2d2d;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #404040;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.activity-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.view-all-btn {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #404040;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-company {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.activity-date {
  color: #888;
  font-size: 12px;
}

.activity-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.activity-status.upcoming {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.activity-status.new-listed {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.activity-status.ongoing {
  background-color: rgba(0, 123, 255, 0.2);
  color: #007bff;
}

/* Auth Required */
.ipo-admin-dashboard .auth-required {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px 20px;
}

.ipo-admin-dashboard .auth-required h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.ipo-admin-dashboard .auth-required p {
  color: #888;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ipo-admin-dashboard .main-content {
    padding: 20px 15px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .board-chart {
    flex-direction: column;
    gap: 20px;
  }
  
  .chart-stats {
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
  }
  
  .ipo-admin-dashboard .sidebar {
    display: none;
  }
}
