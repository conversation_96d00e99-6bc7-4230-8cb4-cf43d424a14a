.upcoming-ipo {
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  padding: 0;
}

.page-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-filter,
.btn-export {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-filter:hover,
.btn-export:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.content-wrapper {
  padding: 32px;
}

.page-description {
  margin-bottom: 32px;
}

.page-description p {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

/* IPO Grid */
.ipo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.ipo-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.ipo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-logo {
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.company-details {
  flex: 1;
}

.company-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.company-sector {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.status-badge {
  padding: 4px 0;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-open {
  background: #dcfce7;
  color: #166534;
}

.status-upcoming {
  background: #fef3c7;
  color: #92400e;
}

.status-closed {
  background: #fee2e2;
  color: #991b1b;
}

/* Card Content */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.price-info {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.price-range,
.min-investment {
  flex: 1;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  text-align: center;
}

.price-range .label,
.min-investment .label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
}

.price-range .value,
.min-investment .value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* Timeline */
.ipo-timeline {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
}

.timeline-item {
  text-align: center;
  flex: 1;
}

.timeline-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
}

.timeline-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

/* Metrics */
.ipo-metrics {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.metric {
  flex: 1;
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.metric-value.subscription {
  color: #dc2626;
}

/* Card Actions */
.card-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f3f4f6;
}

.btn-primary {
  flex: 1;
  padding: 12px 24px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #1d4ed8;
}

.btn-secondary {
  flex: 1;
  padding: 12px 24px;
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .ipo-grid {
    grid-template-columns: 1fr;
  }
  
  .price-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .card-actions {
    flex-direction: column;
  }
}
