import { useState } from 'react';
import { Link } from 'react-router-dom';
import './Blog.css';

const Blog = () => {
  const [blogPosts] = useState([
    {
      id: 1,
      title: 'Top Reasons for Life Insurance Claim Rejection',
      excerpt: 'Understanding the common reasons why life insurance claims get rejected and how to avoid them.',
      date: '04 January 2024',
      readTime: '4 min read',
      category: 'Insurance',
      image: '/api/placeholder/400/250',
      slug: 'life-insurance-claim-rejection'
    },
    {
      id: 2,
      title: 'Best PSU Stocks in India',
      excerpt: 'Comprehensive analysis of the top performing Public Sector Undertaking stocks in the Indian market.',
      date: '20 December 2023',
      readTime: '6 min read',
      category: 'Stocks',
      image: '/api/placeholder/400/250',
      slug: 'best-psu-stocks-india'
    },
    {
      id: 3,
      title: 'Banking & Financial Services Fund',
      excerpt: 'Deep dive into banking and financial services mutual funds and their performance metrics.',
      date: '18 December 2023',
      readTime: '7 min read',
      category: 'Mutual Funds',
      image: '/api/placeholder/400/250',
      slug: 'banking-financial-services-fund'
    },
    {
      id: 4,
      title: 'INOX India IPO - Everything You Must Know',
      excerpt: 'Complete guide to INOX India IPO including company analysis, financials, and investment prospects.',
      date: '11 December 2023',
      readTime: '4 min read',
      category: 'IPO',
      image: '/api/placeholder/400/250',
      slug: 'inox-india-ipo-guide'
    },
    {
      id: 5,
      title: 'Types of Banking Frauds - How to Prevent Them',
      excerpt: 'Learn about different types of banking frauds and effective strategies to protect yourself.',
      date: '08 December 2023',
      readTime: '5 min read',
      category: 'Banking',
      image: '/api/placeholder/400/250',
      slug: 'banking-frauds-prevention'
    },
    {
      id: 6,
      title: 'Popular Finance Podcasts in India',
      excerpt: 'Discover the best finance and investment podcasts to enhance your financial knowledge.',
      date: '30 November 2023',
      readTime: '3 min read',
      category: 'Education',
      image: '/api/placeholder/400/250',
      slug: 'finance-podcasts-india'
    },
    {
      id: 7,
      title: 'Cryptocurrency Investment Strategies',
      excerpt: 'Essential strategies for investing in cryptocurrency and managing digital asset portfolios.',
      date: '25 November 2023',
      readTime: '8 min read',
      category: 'Cryptocurrency',
      image: '/api/placeholder/400/250',
      slug: 'cryptocurrency-investment-strategies'
    },
    {
      id: 8,
      title: 'Tax Saving Investment Options',
      excerpt: 'Comprehensive guide to tax-saving investment options under Section 80C and other provisions.',
      date: '22 November 2023',
      readTime: '6 min read',
      category: 'Tax Planning',
      image: '/api/placeholder/400/250',
      slug: 'tax-saving-investments'
    },
    {
      id: 9,
      title: 'Market Analysis: Q3 2023 Review',
      excerpt: 'Detailed analysis of market performance in Q3 2023 and outlook for the upcoming quarter.',
      date: '15 November 2023',
      readTime: '10 min read',
      category: 'Market Analysis',
      image: '/api/placeholder/400/250',
      slug: 'market-analysis-q3-2023'
    }
  ]);

  const [categories] = useState([
    'All', 'IPO', 'Stocks', 'Mutual Funds', 'Insurance', 'Banking', 'Cryptocurrency', 'Tax Planning', 'Market Analysis', 'Education'
  ]);

  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredPosts = selectedCategory === 'All' 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  return (
    <div className="blog-page">
      {/* Breadcrumb */}
      <div className="breadcrumb-section">
        <div className="container">
          <nav className="breadcrumb">
            <Link to="/" className="breadcrumb-link">Home</Link>
            <span className="breadcrumb-separator">/</span>
            <span className="breadcrumb-current">Blog</span>
          </nav>
        </div>
      </div>

      {/* Header */}
      <section className="blog-header">
        <div className="container">
          <h1 className="blog-title">BLUESTOCK BLOG</h1>
          <p className="blog-subtitle">Stay updated with the latest insights, market trends, and financial knowledge</p>
        </div>
      </section>

      {/* Category Filter */}
      <section className="category-filter">
        <div className="container">
          <div className="category-tabs">
            {categories.map((category) => (
              <button
                key={category}
                className={`category-tab ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Grid */}
      <section className="blog-grid-section">
        <div className="container">
          <div className="blog-grid">
            {filteredPosts.map((post) => (
              <article key={post.id} className="blog-card">
                <div className="blog-card-image">
                  <div className="placeholder-image">
                    <div className="placeholder-content">
                      <h3>{post.title}</h3>
                    </div>
                  </div>
                  <span className="blog-category-badge">{post.category}</span>
                </div>
                
                <div className="blog-card-content">
                  <h2 className="blog-card-title">
                    <Link to={`/blog/${post.slug}`} className="blog-title-link">
                      {post.title}
                    </Link>
                  </h2>
                  
                  <p className="blog-card-excerpt">{post.excerpt}</p>
                  
                  <div className="blog-card-meta">
                    <span className="blog-date">{post.date}</span>
                    <span className="blog-read-time">{post.readTime}</span>
                  </div>
                  
                  <Link to={`/blog/${post.slug}`} className="read-more-btn">
                    Read More
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="newsletter-section">
        <div className="container">
          <div className="newsletter-content">
            <h2>Stay Updated</h2>
            <p>Subscribe to our newsletter for the latest financial insights and market updates</p>
            <div className="newsletter-form">
              <input 
                type="email" 
                placeholder="Enter your email address" 
                className="newsletter-input"
              />
              <button className="newsletter-btn">Subscribe</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
