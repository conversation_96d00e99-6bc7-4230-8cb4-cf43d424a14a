import { Link, useLocation } from 'react-router-dom';
import './AdminSidebar.css';

const AdminSidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      icon: '🏠',
      label: 'Dashboard',
      path: '/admin-dashboard',
      isActive: location.pathname === '/admin-dashboard'
    },
    {
      icon: '📊',
      label: 'Analytics',
      path: '/admin-analytics',
      isActive: location.pathname === '/admin-analytics'
    },
    {
      icon: '📋',
      label: 'IPO Table',
      path: '/ipo-table',
      isActive: location.pathname === '/ipo-table'
    },
    {
      icon: '➕',
      label: 'Register IPO',
      path: '/register-ipo',
      isActive: location.pathname === '/register-ipo'
    },
    {
      icon: '📈',
      label: 'Upcoming IPO',
      path: '/upcoming-ipo',
      isActive: location.pathname === '/upcoming-ipo'
    },
    {
      icon: '📄',
      label: 'Manage IPO',
      path: '/manage-ipo',
      isActive: location.pathname === '/manage-ipo'
    }
  ];

  const otherItems = [
    {
      icon: '⚙️',
      label: 'Settings',
      path: '/admin-settings',
      isActive: location.pathname === '/admin-settings'
    },
    {
      icon: '👥',
      label: 'All Manager',
      path: '/all-manager',
      isActive: location.pathname === '/all-manager'
    },
    {
      icon: '👤',
      label: 'Accounts',
      path: '/accounts',
      isActive: location.pathname === '/accounts'
    },
    {
      icon: '❓',
      label: 'Help',
      path: '/help',
      isActive: location.pathname === '/help'
    }
  ];

  return (
    <div className="admin-sidebar">
      {/* Logo Section */}
      <div className="sidebar-header">
        <div className="logo-container">
          <div className="logo-icon">📈</div>
          <span className="logo-text">Bluestock Fintech</span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <div className="nav-section">
          <h3 className="nav-title">MENU</h3>
          <ul className="nav-list">
            {menuItems.map((item, index) => (
              <li key={index} className={`nav-item ${item.isActive ? 'active' : ''}`}>
                <Link to={item.path} className="nav-link">
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="nav-section">
          <h3 className="nav-title">OTHERS</h3>
          <ul className="nav-list">
            {otherItems.map((item, index) => (
              <li key={index} className={`nav-item ${item.isActive ? 'active' : ''}`}>
                <Link to={item.path} className="nav-link">
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>

      {/* User Profile Section */}
      <div className="sidebar-footer">
        <div className="user-profile">
          <div className="user-avatar">
            <img src="https://via.placeholder.com/40?text=V" alt="User" />
          </div>
          <div className="user-info">
            <span className="user-name">Hi, Vishal</span>
            <span className="user-role">Admin</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
