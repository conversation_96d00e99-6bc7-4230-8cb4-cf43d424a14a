/* Products Page Styles */
.products-page {
  min-height: 100vh;
  background-color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  text-align: center;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 24px 0;
}

.hero-content p {
  font-size: 20px;
  line-height: 1.6;
  margin: 0 0 40px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-lg {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-primary {
  background-color: #ffffff;
  color: #667eea;
  border-color: #ffffff;
}

.btn-primary:hover {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
  transform: translateY(-2px);
}

.btn-outline-primary {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
}

.btn-outline-primary:hover {
  background-color: #ffffff;
  color: #667eea;
  transform: translateY(-2px);
}

.btn-outline-light {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
}

.btn-outline-light:hover {
  background-color: #ffffff;
  color: #667eea;
  transform: translateY(-2px);
}

.btn-outline-secondary {
  background-color: transparent;
  color: #6b7280;
  border-color: #d1d5db;
}

.btn-outline-secondary:hover {
  background-color: #6b7280;
  color: #ffffff;
  transform: translateY(-1px);
}

/* Section Styles */
.products-section,
.technology-section {
  padding: 80px 0;
}

.technology-section {
  background-color: #f8fafc;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.section-header p {
  font-size: 18px;
  color: #666666;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.product-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 30px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: fit-content;
}

.product-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.product-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.product-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.product-icon span {
  font-size: 24px;
}

.product-meta {
  flex: 1;
}

.product-category {
  display: inline-block;
  background-color: #e0e7ff;
  color: #3730a3;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 8px;
}

.product-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
}

.product-description {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.product-features {
  margin-bottom: 30px;
}

.product-features h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.product-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product-features li {
  padding: 6px 0;
  color: #666666;
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.product-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: 600;
}

.product-actions {
  display: flex;
  gap: 12px;
}

.product-actions .btn {
  flex: 1;
  padding: 10px 16px;
  font-size: 14px;
}

/* Technology Grid */
.technology-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.tech-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.tech-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tech-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto;
}

.tech-icon span {
  font-size: 32px;
}

.tech-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.tech-description {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin: 0;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.cta-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.cta-content p {
  font-size: 18px;
  margin: 0 0 32px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 36px;
  }
  
  .hero-content p {
    font-size: 18px;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .product-card {
    padding: 24px;
  }
  
  .product-header {
    flex-direction: column;
    text-align: center;
  }
  
  .product-actions {
    flex-direction: column;
  }
  
  .technology-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .tech-card {
    padding: 20px;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 28px;
  }

  .section-header h2 {
    font-size: 28px;
  }

  .products-grid {
    gap: 20px;
  }

  .product-card {
    padding: 20px;
  }

  .technology-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 32px;
}

/* Learn More Content */
.learn-more-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.learn-more-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 24px 0 12px 0;
}

.learn-more-content p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.learn-more-content ul {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
}

.learn-more-content li {
  padding: 6px 0;
  color: #374151;
  position: relative;
  padding-left: 20px;
}

.learn-more-content li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: 600;
}

/* Demo Content */
.demo-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.demo-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 24px 0 12px 0;
}

.demo-content p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.demo-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 24px 0;
}

.demo-features li {
  padding: 6px 0;
  color: #374151;
  position: relative;
  padding-left: 20px;
}

.demo-features li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: #6366f1;
  font-weight: 600;
}

/* Demo Form */
.demo-form {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group select {
  background: white;
  cursor: pointer;
}

/* Modal Responsive */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-header {
    padding: 20px 24px;
  }

  .modal-header h2 {
    font-size: 18px;
  }

  .modal-body {
    padding: 24px;
  }

  .demo-form {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 16px 20px;
  }

  .modal-header h2 {
    font-size: 16px;
  }

  .modal-body {
    padding: 20px;
  }

  .demo-form {
    padding: 16px;
  }
}
