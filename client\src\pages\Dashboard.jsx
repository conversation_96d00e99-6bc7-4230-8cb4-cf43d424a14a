import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Dashboard.css';

const Dashboard = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="dashboard-page">
        <div className="auth-required">
          <h2>Authentication Required</h2>
          <p>Please sign in to access your dashboard.</p>
          <Link to="/signin" className="btn btn-primary">
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-page">
      <div className="dashboard-container">
        <div className="dashboard-header">
          <div className="user-welcome">
            <img
              src={user.profilePicture || 'https://via.placeholder.com/60?text=User'}
              alt="Profile"
              className="user-avatar"
            />
            <div className="welcome-text">
              <h1>Welcome back, {user.name?.split(' ')[0]}!</h1>
              <p>Here's your investment overview</p>
            </div>
          </div>
        </div>

        <div className="dashboard-grid">
          <div className="dashboard-card">
            <div className="card-header">
              <h3>Profile Information</h3>
              <span className="card-icon">👤</span>
            </div>
            <div className="card-content">
              <div className="info-item">
                <label>Full Name:</label>
                <span>{user.name}</span>
              </div>
              <div className="info-item">
                <label>Email:</label>
                <span>{user.email}</span>
              </div>
              <div className="info-item">
                <label>Status:</label>
                <span className={user.isVerified ? 'status-verified' : 'status-unverified'}>
                  {user.isVerified ? '✅ Verified' : '⚠️ Unverified'}
                </span>
              </div>
              <div className="info-item">
                <label>Login Count:</label>
                <span>{user.loginCount || 1}</span>
              </div>
            </div>
          </div>

          <div className="dashboard-card">
            <div className="card-header">
              <h3>Quick Actions</h3>
              <span className="card-icon">⚡</span>
            </div>
            <div className="card-content">
              <div className="action-buttons">
                <Link to="/ipo-dashboard" className="action-btn">
                  <span className="action-icon">📈</span>
                  <div className="action-text">
                    <h4>Manage IPO</h4>
                    <p>Track and manage IPO investments</p>
                  </div>
                </Link>
                <Link to="/ipo" className="action-btn">
                  <span className="action-icon">📊</span>
                  <div className="action-text">
                    <h4>Browse IPOs</h4>
                    <p>Explore available IPO opportunities</p>
                  </div>
                </Link>
                <Link to="/community" className="action-btn">
                  <span className="action-icon">👥</span>
                  <div className="action-text">
                    <h4>Community</h4>
                    <p>Connect with other investors</p>
                  </div>
                </Link>
              </div>
            </div>
          </div>

          <div className="dashboard-card">
            <div className="card-header">
              <h3>Recent Activity</h3>
              <span className="card-icon">📋</span>
            </div>
            <div className="card-content">
              <div className="activity-list">
                <div className="activity-item">
                  <div className="activity-icon">🔐</div>
                  <div className="activity-details">
                    <p>Signed in via Google OAuth</p>
                    <small>{new Date(user.lastLogin || Date.now()).toLocaleDateString()}</small>
                  </div>
                </div>
                <div className="activity-item">
                  <div className="activity-icon">👤</div>
                  <div className="activity-details">
                    <p>Profile updated</p>
                    <small>{new Date(user.createdAt || Date.now()).toLocaleDateString()}</small>
                  </div>
                </div>
                <div className="activity-item">
                  <div className="activity-icon">🎉</div>
                  <div className="activity-details">
                    <p>Account created</p>
                    <small>{new Date(user.createdAt || Date.now()).toLocaleDateString()}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="dashboard-card">
            <div className="card-header">
              <h3>Account Settings</h3>
              <span className="card-icon">⚙️</span>
            </div>
            <div className="card-content">
              <div className="settings-list">
                <div className="setting-item">
                  <div className="setting-info">
                    <h4>Email Notifications</h4>
                    <p>Receive updates about IPOs and market news</p>
                  </div>
                  <div className="setting-control">
                    <label className="toggle">
                      <input type="checkbox" defaultChecked />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <div className="setting-item">
                  <div className="setting-info">
                    <h4>Two-Factor Authentication</h4>
                    <p>Add an extra layer of security to your account</p>
                  </div>
                  <div className="setting-control">
                    <button className="btn btn-outline btn-sm">Enable</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;