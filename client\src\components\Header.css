/* Header.css - Bluestock Navigation */
.main-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* Logo */
.header-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: inherit;
}

.header-logo .logo-icon {
  width: 28px;
  height: 28px;
}

.header-logo .logo-bars {
  display: flex;
  gap: 2px;
  height: 100%;
  align-items: flex-end;
}

.header-logo .bar {
  width: 5px;
  background: #6366f1;
  border-radius: 1px;
}

.header-logo .bar-1 {
  height: 60%;
}

.header-logo .bar-2 {
  height: 100%;
}

.header-logo .bar-3 {
  height: 80%;
}

.header-logo .logo-text {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: 0.5px;
}

/* Navigation */
.header-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 32px;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 4px;
  text-decoration: none;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 0;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: #6366f1;
}

.dropdown-icon {
  opacity: 0.6;
}

.live-news {
  position: relative;
}

.new-badge {
  background: #6366f1;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 8px;
  margin-left: 6px;
  text-transform: uppercase;
}

/* Auth Buttons */
.header-auth {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auth-btn {
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.signin-btn {
  background: transparent;
  color: #374151;
}

.signin-btn:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.signup-btn {
  background: #6366f1;
  color: white;
}

.signup-btn:hover {
  background: #5856eb;
}

/* User Menu */
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  min-width: 180px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.user-menu:hover .user-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-link {
  display: block;
  padding: 8px 16px;
  color: #374151;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.dropdown-link:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.logout-btn {
  width: 100%;
  background: transparent;
  color: #dc2626;
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  text-align: left;
  border-top: 1px solid #e5e7eb;
  margin-top: 4px;
}

.logout-btn:hover {
  background: #fef2f2;
}

.logout-direct-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 12px;
}

.logout-direct-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: #374151;
  cursor: pointer;
  padding: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-nav {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .header-auth {
    gap: 8px;
  }
  
  .auth-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 16px;
  }
  
  .header-logo .logo-text {
    font-size: 14px;
  }
}
