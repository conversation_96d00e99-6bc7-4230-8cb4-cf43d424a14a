import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Header.css';

const Header = () => {
  const { user, handleLogout } = useAuth();

  return (
    <header className="main-header">
      <div className="header-container">
        {/* Logo */}
        <Link to="/" className="header-logo">
          <div className="logo-icon">
            <div className="logo-bars">
              <div className="bar bar-1"></div>
              <div className="bar bar-2"></div>
              <div className="bar bar-3"></div>
            </div>
          </div>
          <span className="logo-text">BLUESTOCK</span>
        </Link>

        {/* Navigation Menu */}
        <nav className="header-nav">
          <ul className="nav-menu">
            <li className="nav-item">
              <Link to="/ipo" className="nav-link">IPO</Link>
            </li>
            <li className="nav-item">
              <Link to="/community" className="nav-link">COMMUNITY</Link>
            </li>
            <li className="nav-item">
              <Link to="/blog" className="nav-link">BLOG</Link>
            </li>
            <li className="nav-item dropdown">
              <Link to="/products" className="nav-link">
                PRODUCTS
                <svg className="dropdown-icon" width="12" height="8" viewBox="0 0 12 8" fill="none">
                  <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Link>
            </li>
            <li className="nav-item dropdown">
              <Link to="/brokers" className="nav-link">
                BROKERS
                <svg className="dropdown-icon" width="12" height="8" viewBox="0 0 12 8" fill="none">
                  <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Link>
            </li>
            <li className="nav-item">
              <Link to="/live-news" className="nav-link live-news">
                LIVE NEWS
                <span className="new-badge">NEW</span>
              </Link>
            </li>
          </ul>
        </nav>

        {/* Auth Buttons */}
        <div className="header-auth">
          {user ? (
            <div className="user-menu">
              <div className="user-info">
                <img
                  src={user.profilePicture || 'https://via.placeholder.com/32?text=U'}
                  alt="Profile"
                  className="user-avatar"
                />
                <span className="user-name">Hi, {user.name?.split(' ')[0]}!</span>
              </div>
              <div className="user-dropdown">
                <Link to="/dashboard" className="dropdown-link">Dashboard</Link>
                <Link to="/admin-dashboard" className="dropdown-link">Admin Dashboard</Link>
                <Link to="/manage-ipo" className="dropdown-link">Manage IPO</Link>
                <Link to="/ipo-table" className="dropdown-link">IPO Table</Link>
                <Link to="/upcoming-ipo" className="dropdown-link">Upcoming IPOs</Link>
                <button onClick={handleLogout} className="logout-btn">
                  Logout
                </button>
              </div>
              {/* Direct logout button for better visibility */}
              <button onClick={handleLogout} className="auth-btn logout-direct-btn">
                Sign Out
              </button>
            </div>
          ) : (
            <>
              <Link to="/signin" className="auth-btn signin-btn">
                Sign In
              </Link>
              <Link to="/signup" className="auth-btn signup-btn">
                Sign Up
              </Link>
            </>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button className="mobile-menu-btn">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </header>
  );
};

export default Header;
