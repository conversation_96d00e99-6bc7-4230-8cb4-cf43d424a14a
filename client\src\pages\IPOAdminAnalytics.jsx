import { useState } from 'react';
import AdminSidebar from '../components/AdminSidebar';
import './IPOAdminAnalytics.css';

const IPOAdminAnalytics = () => {
  const [analyticsData] = useState({
    quickStats: {
      totalIPOs: 156,
      activeIPOs: 23,
      completedIPOs: 133,
      totalValue: '₹ 2,45,678 Cr'
    },
    chartData: {
      monthlyIPOs: [
        { month: 'Jan', count: 12 },
        { month: 'Feb', count: 18 },
        { month: 'Mar', count: 15 },
        { month: 'Apr', count: 22 },
        { month: 'May', count: 28 },
        { month: 'Jun', count: 25 }
      ],
      sectorDistribution: [
        { sector: 'Technology', percentage: 35, color: '#3b82f6' },
        { sector: 'Healthcare', percentage: 25, color: '#10b981' },
        { sector: 'Finance', percentage: 20, color: '#f59e0b' },
        { sector: 'Manufacturing', percentage: 15, color: '#ef4444' },
        { sector: 'Others', percentage: 5, color: '#8b5cf6' }
      ]
    },
    recentIPOs: [
      {
        id: 1,
        company: 'TechCorp Ltd',
        sector: 'Technology',
        issueSize: '₹ 2,500 Cr',
        status: 'Active',
        subscriptionRate: '2.5x'
      },
      {
        id: 2,
        company: 'HealthPlus Inc',
        sector: 'Healthcare',
        issueSize: '₹ 1,800 Cr',
        status: 'Completed',
        subscriptionRate: '3.2x'
      },
      {
        id: 3,
        company: 'FinanceFirst',
        sector: 'Finance',
        issueSize: '₹ 3,200 Cr',
        status: 'Upcoming',
        subscriptionRate: '-'
      }
    ]
  });

  return (
    <div className="ipo-admin-analytics">
      <AdminSidebar />
      
      <div className="main-content">
        <div className="page-header">
          <h1>IPO Dashboard India</h1>
          <div className="header-actions">
            <button className="btn-export">Export Report</button>
            <button className="btn-refresh">🔄</button>
          </div>
        </div>

        <div className="content-wrapper">
          {/* Quick Stats */}
          <div className="quick-stats">
            <h2>Quick Stats</h2>
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">📊</div>
                <div className="stat-content">
                  <h3>{analyticsData.quickStats.totalIPOs}</h3>
                  <p>Total IPOs</p>
                </div>
              </div>
              <div className="stat-card active">
                <div className="stat-icon">🚀</div>
                <div className="stat-content">
                  <h3>{analyticsData.quickStats.activeIPOs}</h3>
                  <p>Active IPOs</p>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">✅</div>
                <div className="stat-content">
                  <h3>{analyticsData.quickStats.completedIPOs}</h3>
                  <p>Completed IPOs</p>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">💰</div>
                <div className="stat-content">
                  <h3>{analyticsData.quickStats.totalValue}</h3>
                  <p>Total Value</p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="charts-section">
            <div className="chart-container">
              <h3>Monthly IPO Trends</h3>
              <div className="bar-chart">
                {analyticsData.chartData.monthlyIPOs.map((data, index) => (
                  <div key={index} className="bar-item">
                    <div 
                      className="bar" 
                      style={{ height: `${(data.count / 30) * 100}%` }}
                    ></div>
                    <span className="bar-label">{data.month}</span>
                    <span className="bar-value">{data.count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="chart-container">
              <h3>Sector Distribution</h3>
              <div className="pie-chart">
                <div className="pie-visual">
                  <div className="pie-center">
                    <span className="pie-total">100%</span>
                    <span className="pie-label">Total</span>
                  </div>
                </div>
                <div className="pie-legend">
                  {analyticsData.chartData.sectorDistribution.map((sector, index) => (
                    <div key={index} className="legend-item">
                      <div 
                        className="legend-color" 
                        style={{ backgroundColor: sector.color }}
                      ></div>
                      <span className="legend-text">{sector.sector}</span>
                      <span className="legend-percentage">{sector.percentage}%</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Recent IPOs Table */}
          <div className="recent-ipos">
            <h3>Recent IPO Activity</h3>
            <div className="table-wrapper">
              <table className="recent-table">
                <thead>
                  <tr>
                    <th>Company</th>
                    <th>Sector</th>
                    <th>Issue Size</th>
                    <th>Status</th>
                    <th>Subscription</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {analyticsData.recentIPOs.map((ipo) => (
                    <tr key={ipo.id}>
                      <td className="company-name">{ipo.company}</td>
                      <td>{ipo.sector}</td>
                      <td className="issue-size">{ipo.issueSize}</td>
                      <td>
                        <span className={`status-badge status-${ipo.status.toLowerCase()}`}>
                          {ipo.status}
                        </span>
                      </td>
                      <td className="subscription-rate">{ipo.subscriptionRate}</td>
                      <td>
                        <button className="btn-view-details">View</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Main Board IPO Section */}
          <div className="main-board-section">
            <h2>Main Board IPO</h2>
            <div className="board-stats">
              <div className="board-card">
                <div className="board-header">
                  <h4>This Month</h4>
                  <span className="board-trend">↗️ +15%</span>
                </div>
                <div className="board-value">₹ 12,450 Cr</div>
                <div className="board-count">8 IPOs</div>
              </div>
              <div className="board-card">
                <div className="board-header">
                  <h4>This Quarter</h4>
                  <span className="board-trend">↗️ +22%</span>
                </div>
                <div className="board-value">₹ 45,680 Cr</div>
                <div className="board-count">25 IPOs</div>
              </div>
              <div className="board-card">
                <div className="board-header">
                  <h4>This Year</h4>
                  <span className="board-trend">↗️ +18%</span>
                </div>
                <div className="board-value">₹ 1,85,420 Cr</div>
                <div className="board-count">89 IPOs</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPOAdminAnalytics;
