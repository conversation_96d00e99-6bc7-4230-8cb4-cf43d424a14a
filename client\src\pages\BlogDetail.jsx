import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import './BlogDetail.css';

const BlogDetail = () => {
  const { slug } = useParams();
  const [article, setArticle] = useState(null);
  const [topGainers] = useState([
    { name: 'Info Edge India', price: '₹ 5,246.15', change: '+2.45%', changeClass: 'positive' },
    { name: 'Adani Ports', price: '₹ 1,194.25', change: '+1.87%', changeClass: 'positive' },
    { name: 'Zomato', price: '₹ 132.30', change: '+3.21%', changeClass: 'positive' },
    { name: 'Future Traders', price: '₹ 2,421.10', change: '+1.56%', changeClass: 'positive' },
    { name: 'Nykaa', price: '₹ 178.45', change: '+2.89%', changeClass: 'positive' }
  ]);

  const [topLosers] = useState([
    { name: '<PERSON><PERSON>na <PERSON>', price: '₹ 2,031.30', change: '-1.23%', changeClass: 'negative' },
    { name: 'Chemplast Sanmar', price: '₹ 1,431.85', change: '-2.45%', changeClass: 'negative' },
    { name: 'Bajaj Holdings', price: '₹ 8,046.10', change: '-0.87%', changeClass: 'negative' },
    { name: 'Nuvoco Vistas', price: '₹ 2,728.80', change: '-1.65%', changeClass: 'negative' },
    { name: 'Nuvoco India', price: '₹ 2,488.40', change: '-2.12%', changeClass: 'negative' }
  ]);

  // Sample article data - in real app this would come from API
  useEffect(() => {
    const sampleArticle = {
      title: 'Key Things to Know about Inox India IPO - Inox India IPO - Everything You Must Know',
      date: '21 December 2023',
      readTime: '4 min read',
      category: 'IPO',
      content: `
        <div class="article-hero-image">
          <div class="ipo-hero-placeholder">
            <div class="ipo-logo-section">
              <div class="ipo-building-icon">🏢</div>
              <h2>Inox India IPO</h2>
            </div>
          </div>
        </div>

        <p>One of the leading manufacturers of cryogenic storage tanks, Inox India, is about to launch its IPO on 21 December 2023. The much-awaited IPO is expected to boost the overall stock market scenario.</p>

        <h2>About Inox India Limited (INOXCVA)</h2>
        
        <p>The Gujarat-based company, Inox India is a supplier of cryogenic equipment that excels in offering end-to-end solutions for equipment manufacturing. The company specializes in manufacturing cryogenic containers, from designing to engineering to manufacturing. INOXCVA offers Turnkey Plant Solutions of products and services.</p>

        <p>With its primary division, i.e. Industrial Gas, LNG and Cryo Scientific, the company caters to various industries including oil, gas, healthcare, green hydrogen, chemicals, energy, aviation, and more.</p>

        <div class="ipo-details-table">
          <h3>IPO Details</h3>
          <table>
            <tr>
              <td><strong>IPO Date</strong></td>
              <td>14 December, 2023 to 18 December, 2023</td>
            </tr>
            <tr>
              <td><strong>Price Band</strong></td>
              <td>Rs 627 to Rs 650 per share</td>
            </tr>
            <tr>
              <td><strong>Face Value</strong></td>
              <td>Rs 2 per share</td>
            </tr>
            <tr>
              <td><strong>Lot Size</strong></td>
              <td>22 Shares</td>
            </tr>
            <tr>
              <td><strong>Total Issue Size</strong></td>
              <td>22,110,955 shares (aggregating up to Rs 1,439.32 Cr)</td>
            </tr>
            <tr>
              <td><strong>Offer for Sale</strong></td>
              <td>22,110,955 shares of Rs 2 (aggregating up to Rs 1,439.32 Cr)</td>
            </tr>
          </table>
        </div>

        <h3>• Inox India IPO Date</h3>
        <p>The Inox India IPO window for subscription on 14th November 2023 and will continue till 16th November 2023.</p>

        <h3>• Listing Date</h3>
        <p>Inox India shares trading will begin on 21 December 2023 as per the schedule.</p>
      `,
      tags: ['IPO', 'Stock Market', 'Investment', 'Inox India']
    };
    setArticle(sampleArticle);
  }, [slug]);

  if (!article) {
    return <div className="loading">Loading...</div>;
  }

  return (
    <div className="blog-detail-page">
      {/* Breadcrumb */}
      <div className="breadcrumb-section">
        <div className="container">
          <nav className="breadcrumb">
            <Link to="/" className="breadcrumb-link">Home</Link>
            <span className="breadcrumb-separator">/</span>
            <Link to="/blog" className="breadcrumb-link">Blog</Link>
            <span className="breadcrumb-separator">/</span>
            <span className="breadcrumb-current">IPO Industry</span>
          </nav>
        </div>
      </div>

      <div className="article-container">
        <div className="container">
          <div className="article-layout">
            {/* Main Article */}
            <article className="article-main">
              <header className="article-header">
                <h1 className="article-title">{article.title}</h1>
                <div className="article-meta">
                  <span className="article-date">{article.date}</span>
                  <span className="article-read-time">{article.readTime}</span>
                </div>
              </header>

              <div className="article-content" dangerouslySetInnerHTML={{ __html: article.content }} />

              <div className="article-tags">
                {article.tags.map((tag, index) => (
                  <span key={index} className="article-tag">#{tag}</span>
                ))}
              </div>

              <div className="article-actions">
                <button className="share-btn">Share Article</button>
                <button className="bookmark-btn">Bookmark</button>
              </div>
            </article>

            {/* Sidebar */}
            <aside className="article-sidebar">
              <div className="sidebar-section">
                <h3 className="sidebar-title">Recent Post</h3>
                <div className="recent-post-placeholder">
                  <div className="recent-post-content">
                    <h4>Latest Market Updates</h4>
                    <p>Stay updated with recent market trends</p>
                  </div>
                </div>
              </div>

              <div className="sidebar-section">
                <h3 className="sidebar-title">Download</h3>
                <div className="download-placeholder">
                  <div className="download-content">
                    <h4>Investment Guide</h4>
                    <button className="download-btn">Download PDF</button>
                  </div>
                </div>
              </div>

              <div className="sidebar-section">
                <h3 className="sidebar-title">Top Gainers</h3>
                <div className="stock-list">
                  {topGainers.map((stock, index) => (
                    <div key={index} className="stock-item">
                      <div className="stock-info">
                        <span className="stock-name">{stock.name}</span>
                        <span className="stock-price">{stock.price}</span>
                      </div>
                      <span className={`stock-change ${stock.changeClass}`}>
                        {stock.change}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="sidebar-section">
                <h3 className="sidebar-title">Top Losers</h3>
                <div className="stock-list">
                  {topLosers.map((stock, index) => (
                    <div key={index} className="stock-item">
                      <div className="stock-info">
                        <span className="stock-name">{stock.name}</span>
                        <span className="stock-price">{stock.price}</span>
                      </div>
                      <span className={`stock-change ${stock.changeClass}`}>
                        {stock.change}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </aside>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogDetail;
