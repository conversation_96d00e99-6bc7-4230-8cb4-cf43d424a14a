import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import SignIn from './pages/SignIn';
import SignUp from './pages/SignUp';
import Dashboard from './pages/Dashboard';
import IPODashboard from './pages/IPODashboard';
import ProtectedRoute from './components/ProtectedRoute';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="app">
          <Header />
          <main className="main-content">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/signin" element={<SignIn />} />
              <Route path="/signup" element={<SignUp />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/ipo-dashboard"
                element={
                  <ProtectedRoute>
                    <IPODashboard />
                  </ProtectedRoute>
                }
              />
              {/* Placeholder routes for navigation links */}
              <Route path="/ipo" element={<div className="page-placeholder"><h2>IPO Page</h2><p>Coming Soon...</p></div>} />
              <Route path="/community" element={<div className="page-placeholder"><h2>Community Page</h2><p>Coming Soon...</p></div>} />
              <Route path="/products" element={<div className="page-placeholder"><h2>Products Page</h2><p>Coming Soon...</p></div>} />
              <Route path="/brokers" element={<div className="page-placeholder"><h2>Brokers Page</h2><p>Coming Soon...</p></div>} />
              <Route path="/live-news" element={<div className="page-placeholder"><h2>Live News Page</h2><p>Coming Soon...</p></div>} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;