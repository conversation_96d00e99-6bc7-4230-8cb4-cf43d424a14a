import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import SignIn from './pages/SignIn';
import SignUp from './pages/SignUp';
import Dashboard from './pages/Dashboard';
import IPODashboard from './pages/IPODashboard';
import IPOManagement from './pages/IPOManagement';
import IPOAdminDashboard from './pages/IPOAdminDashboard';
import IPOAdminAnalytics from './pages/IPOAdminAnalytics';
import IPOTableDashboard from './pages/IPOTableDashboard';
import RegisterIPO from './pages/RegisterIPO';
import UpcomingIPO from './pages/UpcomingIPO';
import IPOHome from './pages/IPOHome';
import Products from './pages/Products';
import Blog from './pages/Blog';
import BlogDetail from './pages/BlogDetail';
import ProtectedRoute from './components/ProtectedRoute';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="app">
          <Header />
          <main className="main-content">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/signin" element={<SignIn />} />
              <Route path="/signup" element={<SignUp />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/ipo-dashboard"
                element={
                  <ProtectedRoute>
                    <IPODashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/manage-ipo"
                element={
                  <ProtectedRoute>
                    <IPOManagement />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin-dashboard"
                element={
                  <ProtectedRoute>
                    <IPOAdminDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/ipo-table"
                element={
                  <ProtectedRoute>
                    <IPOTableDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin-analytics"
                element={
                  <ProtectedRoute>
                    <IPOAdminAnalytics />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/register-ipo"
                element={
                  <ProtectedRoute>
                    <RegisterIPO />
                  </ProtectedRoute>
                }
              />
              {/* Public IPO Pages */}
              <Route path="/ipo" element={<IPOHome />} />
              <Route path="/upcoming-ipo" element={<UpcomingIPO />} />
              <Route path="/products" element={<Products />} />
              {/* Blog Pages */}
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:slug" element={<BlogDetail />} />
              {/* Placeholder routes for navigation links */}
              <Route path="/community" element={<div className="page-placeholder"><h2>Community Page</h2><p>Coming Soon...</p></div>} />
              <Route path="/brokers" element={<div className="page-placeholder"><h2>Brokers Page</h2><p>Coming Soon...</p></div>} />
              <Route path="/live-news" element={<div className="page-placeholder"><h2>Live News Page</h2><p>Coming Soon...</p></div>} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;