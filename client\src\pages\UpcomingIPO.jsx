import { useState } from 'react';
import './UpcomingIPO.css';

const UpcomingIPO = () => {
  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState({ type: '', company: '' });

  const [ipoData] = useState([
    {
      id: 1,
      company: 'Nova Agritech Ltd.',
      logo: '🌾',
      priceRange: 'Rs. 39 - 41',
      open: '2024-01-22',
      close: '2024-01-24',
      issueSize: '143.81 Cr.',
      issueType: 'Book Built',
      listingDate: '2024-01-30',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 2,
      company: 'EPACK Durable Ltd.',
      logo: '📦',
      priceRange: 'Rs. 218 - 230',
      open: '2024-01-19',
      close: '2024-01-23',
      issueSize: '640.05 Cr.',
      issueType: 'Book Built',
      listingDate: '2024-01-29',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 3,
      company: 'RK <PERSON>wamy Ltd.',
      logo: '🎯',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 4,
      company: 'Cromwell Stays Ltd.',
      logo: '🏨',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 5,
      company: 'Imagine marketing Ltd.',
      logo: '💡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '2000 cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 6,
      company: 'Ixigo Clinic India Ltd.',
      logo: '🏥',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 7,
      company: 'OLA Electric Mobility Ltd.',
      logo: '⚡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1800 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 8,
      company: 'One MobiKwik Systems Ltd.',
      logo: '💳',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1900 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 9,
      company: '16 Travenues Technology',
      logo: '✈️',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1600 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 10,
      company: 'CMR Green Technologies',
      logo: '🌱',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 11,
      company: 'Wellness Forever',
      logo: '💊',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 12,
      company: 'PNH Ventures Ltd.',
      logo: '🏢',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    }
  ]);

  const handleRHP = (company) => {
    setModalContent({ type: 'RHP', company });
    setShowModal(true);
  };

  const handleDRHP = (company) => {
    setModalContent({ type: 'DRHP', company });
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setModalContent({ type: '', company: '' });
  };

  return (
    <div className="upcoming-ipo">
      <div className="container">
        <div className="page-header">
          <h1>Upcoming IPO</h1>
          <p>Get updates and track the list of companies that you might want to watch for and IPO. Few details might be disclosed by the companies when an IPO gets listed.</p>
        </div>

        <div className="ipo-grid">
          {ipoData.map((ipo) => (
            <div key={ipo.id} className="ipo-card">
              <div className="ipo-header">
                <div className="company-info">
                  <div className="company-logo">
                    <span className="logo-icon">{ipo.logo}</span>
                  </div>
                  <div className="company-details">
                    <h3 className="company-name">{ipo.company}</h3>
                  </div>
                </div>
              </div>

              <div className="ipo-details">
                <div className="detail-row">
                  <div className="detail-item">
                    <label>PRICE BAND</label>
                    <span className={ipo.priceRange === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.priceRange}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>OPEN</label>
                    <span className={ipo.open === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.open}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>CLOSE</label>
                    <span className={ipo.close === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.close}
                    </span>
                  </div>
                </div>

                <div className="detail-row">
                  <div className="detail-item">
                    <label>ISSUE SIZE</label>
                    <span className={ipo.issueSize === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.issueSize}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>ISSUE TYPE</label>
                    <span className="issued">{ipo.issueType}</span>
                  </div>
                  <div className="detail-item">
                    <label>LISTING DATE</label>
                    <span className={ipo.listingDate === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.listingDate}
                    </span>
                  </div>
                </div>
              </div>

              <div className="ipo-actions">
                <button
                  className="btn btn-outline-primary"
                  onClick={() => handleRHP(ipo.company)}
                >
                  RHP
                </button>
                <button
                  className="btn btn-primary"
                  onClick={() => handleDRHP(ipo.company)}
                >
                  DRHP
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal for RHP/DRHP */}
      {showModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{modalContent.type} Document - {modalContent.company}</h2>
              <button className="modal-close" onClick={closeModal}>×</button>
            </div>

            <div className="modal-body">
              <div className="document-info">
                <div className="document-icon">
                  📄
                </div>
                <h3>{modalContent.type === 'RHP' ? 'Red Herring Prospectus' : 'Draft Red Herring Prospectus'}</h3>
                <p>
                  {modalContent.type === 'RHP'
                    ? 'The Red Herring Prospectus contains detailed information about the company, its business model, financial statements, risk factors, and terms of the IPO.'
                    : 'The Draft Red Herring Prospectus is the preliminary version that provides initial details about the IPO before the final pricing and allocation.'
                  }
                </p>

                <div className="document-details">
                  <div className="detail-item">
                    <strong>Company:</strong> {modalContent.company}
                  </div>
                  <div className="detail-item">
                    <strong>Document Type:</strong> {modalContent.type}
                  </div>
                  <div className="detail-item">
                    <strong>Status:</strong> Available for Download
                  </div>
                  <div className="detail-item">
                    <strong>File Format:</strong> PDF
                  </div>
                </div>

                <div className="document-actions">
                  <button className="btn btn-primary btn-lg">
                    📥 Download {modalContent.type}
                  </button>
                  <button className="btn btn-outline-secondary">
                    👁️ Preview Document
                  </button>
                </div>

                <div className="document-note">
                  <p><strong>Note:</strong> Please read the document carefully before making any investment decisions. Past performance is not indicative of future results.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpcomingIPO;
