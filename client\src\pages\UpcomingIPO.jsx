import { useState } from 'react';
import './UpcomingIPO.css';

const UpcomingIPO = () => {
  const [ipoData] = useState([
    {
      id: 1,
      company: 'Nova Agritech Ltd.',
      logo: '🌾',
      priceRange: 'Rs. 39 - 41',
      open: '2024-01-22',
      close: '2024-01-24',
      issueSize: '143.81 Cr.',
      issueType: 'Book Built',
      listingDate: '2024-01-30',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 2,
      company: 'EPACK Durable Ltd.',
      logo: '📦',
      priceRange: 'Rs. 218 - 230',
      open: '2024-01-19',
      close: '2024-01-23',
      issueSize: '640.05 Cr.',
      issueType: 'Book Built',
      listingDate: '2024-01-29',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 3,
      company: 'RK Swamy Ltd.',
      logo: '🎯',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 4,
      company: 'Cromwell Stays Ltd.',
      logo: '🏨',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 5,
      company: 'Imagine marketing Ltd.',
      logo: '💡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '2000 cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 6,
      company: 'Ixigo Clinic India Ltd.',
      logo: '🏥',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 7,
      company: 'OLA Electric Mobility Ltd.',
      logo: '⚡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1800 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 8,
      company: 'One MobiKwik Systems Ltd.',
      logo: '💳',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1900 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 9,
      company: '16 Travenues Technology',
      logo: '✈️',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1600 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 10,
      company: 'CMR Green Technologies',
      logo: '🌱',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 11,
      company: 'Wellness Forever',
      logo: '💊',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 12,
      company: 'PNH Ventures Ltd.',
      logo: '🏢',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    }
  ]);

  return (
    <div className="upcoming-ipo">
      <div className="container">
        <div className="page-header">
          <h1>Upcoming IPO</h1>
          <p>Get updates and track the list of companies that you might want to watch for and IPO. Few details might be disclosed by the companies when an IPO gets listed.</p>
        </div>

        <div className="ipo-grid">
          {ipoData.map((ipo) => (
            <div key={ipo.id} className="ipo-card">
              <div className="ipo-header">
                <div className="company-info">
                  <div className="company-logo">
                    <span className="logo-icon">{ipo.logo}</span>
                  </div>
                  <div className="company-details">
                    <h3 className="company-name">{ipo.company}</h3>
                  </div>
                </div>
              </div>

              <div className="ipo-details">
                <div className="detail-row">
                  <div className="detail-item">
                    <label>PRICE BAND</label>
                    <span className={ipo.priceRange === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.priceRange}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>OPEN</label>
                    <span className={ipo.open === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.open}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>CLOSE</label>
                    <span className={ipo.close === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.close}
                    </span>
                  </div>
                </div>

                <div className="detail-row">
                  <div className="detail-item">
                    <label>ISSUE SIZE</label>
                    <span className={ipo.issueSize === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.issueSize}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>ISSUE TYPE</label>
                    <span className="issued">{ipo.issueType}</span>
                  </div>
                  <div className="detail-item">
                    <label>LISTING DATE</label>
                    <span className={ipo.listingDate === 'Not Issued' ? 'not-issued' : 'issued'}>
                      {ipo.listingDate}
                    </span>
                  </div>
                </div>
              </div>

              <div className="ipo-actions">
                <button className="btn btn-outline-primary">RHP</button>
                <button className="btn btn-primary">DRHP</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UpcomingIPO;
