import { useState } from 'react';
import AdminSidebar from '../components/AdminSidebar';
import './UpcomingIPO.css';

const UpcomingIPO = () => {
  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState({ type: '', company: '' });

  const [ipoData] = useState([
    {
      id: 1,
      company: 'Adani Power',
      logo: '🔋',
      sector: 'Power & Energy',
      priceRange: '₹ 326 - 340',
      open: '2024-06-03',
      close: '2024-06-05',
      issueSize: '₹ 43,250.15 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Open',
      subscription: '2.5x',
      minInvestment: '₹ 14,000'
    },
    {
      id: 2,
      company: 'VBL LTD',
      logo: '🥤',
      sector: 'FMCG',
      priceRange: '₹ 230 - 340',
      open: '2024-06-03',
      close: '2024-06-05',
      issueSize: '₹ 13,150.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2018-06-10',
      status: 'Upcoming',
      subscription: '-',
      minInvestment: '₹ 12,000'
    },
    {
      id: 3,
      company: 'Tata Motor',
      logo: '🚗',
      sector: 'Automobile',
      priceRange: '₹ 1,250 - 1,350',
      open: '2024-06-08',
      close: '2024-06-09',
      issueSize: '₹ 15,400.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2016-06-10',
      status: 'Closed',
      subscription: '3.8x',
      minInvestment: '₹ 18,000'
    },
    {
      id: 4,
      company: 'Cromwell Stays Ltd.',
      logo: '🏨',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 5,
      company: 'Imagine marketing Ltd.',
      logo: '💡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '2000 cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 6,
      company: 'Ixigo Clinic India Ltd.',
      logo: '🏥',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 7,
      company: 'OLA Electric Mobility Ltd.',
      logo: '⚡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1800 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 8,
      company: 'One MobiKwik Systems Ltd.',
      logo: '💳',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1900 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 9,
      company: '16 Travenues Technology',
      logo: '✈️',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1600 Cr.',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 10,
      company: 'CMR Green Technologies',
      logo: '🌱',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 11,
      company: 'Wellness Forever',
      logo: '💊',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    },
    {
      id: 12,
      company: 'PNH Ventures Ltd.',
      logo: '🏢',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: 'Not Issued',
      issueType: 'Book Built',
      listingDate: 'Not Issued',
      rhpLink: '#',
      drhpLink: '#'
    }
  ]);

  const handleRHP = (company) => {
    setModalContent({ type: 'RHP', company });
    setShowModal(true);
  };

  const handleDRHP = (company) => {
    setModalContent({ type: 'DRHP', company });
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setModalContent({ type: '', company: '' });
  };

  return (
    <div className="upcoming-ipo">
      <AdminSidebar />

      <div className="main-content">
        <div className="page-header">
          <h1>Upcoming IPO</h1>
          <div className="header-actions">
            <button className="btn-filter">Filter</button>
            <button className="btn-export">Export</button>
          </div>
        </div>

        <div className="content-wrapper">
          <div className="page-description">
            <p>Get updates and track the list of companies that you might want to watch for and IPO. Few details might be disclosed by the companies when an IPO gets listed.</p>
          </div>

        <div className="ipo-grid">
          {ipoData.map((ipo) => (
            <div key={ipo.id} className="ipo-card">
              <div className="card-header">
                <div className="company-info">
                  <div className="company-logo">{ipo.logo}</div>
                  <div className="company-details">
                    <h3 className="company-name">{ipo.company}</h3>
                    <p className="company-sector">{ipo.sector}</p>
                  </div>
                </div>
                <div className="status-badge">
                  <span className={`status status-${ipo.status.toLowerCase()}`}>
                    {ipo.status}
                  </span>
                </div>
              </div>

              <div className="card-content">
                <div className="price-info">
                  <div className="price-range">
                    <span className="label">Price Band</span>
                    <span className="value">{ipo.priceRange}</span>
                  </div>
                  <div className="min-investment">
                    <span className="label">Min Investment</span>
                    <span className="value">{ipo.minInvestment}</span>
                  </div>
                </div>

                <div className="ipo-timeline">
                  <div className="timeline-item">
                    <span className="timeline-label">Open</span>
                    <span className="timeline-value">{ipo.open}</span>
                  </div>
                  <div className="timeline-item">
                    <span className="timeline-label">Close</span>
                    <span className="timeline-value">{ipo.close}</span>
                  </div>
                  <div className="timeline-item">
                    <span className="timeline-label">Listing</span>
                    <span className="timeline-value">{ipo.listingDate}</span>
                  </div>
                </div>

                <div className="ipo-metrics">
                  <div className="metric">
                    <span className="metric-label">Issue Size</span>
                    <span className="metric-value">{ipo.issueSize}</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Subscription</span>
                    <span className="metric-value subscription">{ipo.subscription}</span>
                  </div>
                </div>
              </div>

              <div className="card-actions">
                <button className="btn-primary">Apply Now</button>
                <button className="btn-secondary">View Details</button>
              </div>
            </div>
          ))}
        </div>
        </div>
      </div>

      {/* Modal for RHP/DRHP */}
      {showModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{modalContent.type} Document - {modalContent.company}</h2>
              <button className="modal-close" onClick={closeModal}>×</button>
            </div>

            <div className="modal-body">
              <div className="document-info">
                <div className="document-icon">
                  📄
                </div>
                <h3>{modalContent.type === 'RHP' ? 'Red Herring Prospectus' : 'Draft Red Herring Prospectus'}</h3>
                <p>
                  {modalContent.type === 'RHP'
                    ? 'The Red Herring Prospectus contains detailed information about the company, its business model, financial statements, risk factors, and terms of the IPO.'
                    : 'The Draft Red Herring Prospectus is the preliminary version that provides initial details about the IPO before the final pricing and allocation.'
                  }
                </p>

                <div className="document-details">
                  <div className="detail-item">
                    <strong>Company:</strong> {modalContent.company}
                  </div>
                  <div className="detail-item">
                    <strong>Document Type:</strong> {modalContent.type}
                  </div>
                  <div className="detail-item">
                    <strong>Status:</strong> Available for Download
                  </div>
                  <div className="detail-item">
                    <strong>File Format:</strong> PDF
                  </div>
                </div>

                <div className="document-actions">
                  <button className="btn btn-primary btn-lg">
                    📥 Download {modalContent.type}
                  </button>
                  <button className="btn btn-outline-secondary">
                    👁️ Preview Document
                  </button>
                </div>

                <div className="document-note">
                  <p><strong>Note:</strong> Please read the document carefully before making any investment decisions. Past performance is not indicative of future results.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpcomingIPO;
