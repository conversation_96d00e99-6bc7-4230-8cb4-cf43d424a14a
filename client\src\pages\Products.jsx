import { useState } from 'react';
import './Products.css';

const Products = () => {
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'learn-more' or 'demo'

  const [products] = useState([
    {
      id: 1,
      title: 'IPO Management Platform',
      description: 'Comprehensive platform for managing IPO applications, tracking status, and analyzing market trends.',
      features: [
        'Real-time IPO tracking',
        'Application management',
        'Market analysis tools',
        'Portfolio tracking'
      ],
      icon: '📊',
      category: 'Platform'
    },
    {
      id: 2,
      title: 'API Integration Suite',
      description: 'Robust API suite for developers to integrate IPO data and functionality into their applications.',
      features: [
        'RESTful APIs',
        'Real-time data feeds',
        'Webhook support',
        'Developer documentation'
      ],
      icon: '🔗',
      category: 'API'
    },
    {
      id: 3,
      title: 'Analytics Dashboard',
      description: 'Advanced analytics and reporting tools for institutional investors and fund managers.',
      features: [
        'Custom dashboards',
        'Performance metrics',
        'Risk analysis',
        'Automated reporting'
      ],
      icon: '📈',
      category: 'Analytics'
    },
    {
      id: 4,
      title: 'Mobile Application',
      description: 'Native mobile app for iOS and Android with full IPO management capabilities.',
      features: [
        'Mobile-first design',
        'Push notifications',
        'Offline capabilities',
        'Biometric security'
      ],
      icon: '📱',
      category: 'Mobile'
    },
    {
      id: 5,
      title: 'Compliance Suite',
      description: 'Comprehensive compliance and regulatory tools for financial institutions.',
      features: [
        'Regulatory reporting',
        'Audit trails',
        'Risk management',
        'Compliance monitoring'
      ],
      icon: '🛡️',
      category: 'Compliance'
    },
    {
      id: 6,
      title: 'White Label Solution',
      description: 'Customizable white-label platform for brokers and financial service providers.',
      features: [
        'Custom branding',
        'Flexible configuration',
        'Multi-tenant support',
        'Dedicated support'
      ],
      icon: '🏷️',
      category: 'Enterprise'
    }
  ]);

  const [technologies] = useState([
    {
      name: 'React & Node.js',
      description: 'Modern web technologies for scalable applications',
      icon: '⚛️'
    },
    {
      name: 'Cloud Infrastructure',
      description: 'AWS/Azure cloud services for reliability and scale',
      icon: '☁️'
    },
    {
      name: 'Real-time Data',
      description: 'WebSocket connections for live market updates',
      icon: '⚡'
    },
    {
      name: 'Security First',
      description: 'Bank-grade security with encryption and compliance',
      icon: '🔒'
    },
    {
      name: 'AI & Machine Learning',
      description: 'Intelligent insights and predictive analytics',
      icon: '🤖'
    },
    {
      name: 'Microservices',
      description: 'Scalable architecture with independent services',
      icon: '🔧'
    }
  ]);

  const handleLearnMore = (product) => {
    setSelectedProduct(product);
    setModalType('learn-more');
    setShowModal(true);
  };

  const handleTryDemo = (product) => {
    setSelectedProduct(product);
    setModalType('demo');
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedProduct(null);
    setModalType('');
  };

  return (
    <div className="products-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Powerful Products for Modern Finance</h1>
            <p>Discover our comprehensive suite of financial technology solutions designed to streamline IPO management and investment processes.</p>
            <div className="hero-actions">
              <button className="btn btn-primary btn-lg">Explore Products</button>
              <button className="btn btn-outline-primary btn-lg">Request Demo</button>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="products-section">
        <div className="container">
          <div className="section-header">
            <h2>Our Product Suite</h2>
            <p>Comprehensive solutions for every aspect of IPO management and investment</p>
          </div>
          
          <div className="products-grid">
            {products.map((product) => (
              <div key={product.id} className="product-card">
                <div className="product-header">
                  <div className="product-icon">
                    <span>{product.icon}</span>
                  </div>
                  <div className="product-meta">
                    <span className="product-category">{product.category}</span>
                    <h3 className="product-title">{product.title}</h3>
                  </div>
                </div>
                
                <p className="product-description">{product.description}</p>
                
                <div className="product-features">
                  <h4>Key Features</h4>
                  <ul>
                    {product.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
                
                <div className="product-actions">
                  <button
                    className="btn btn-primary"
                    onClick={() => handleLearnMore(product)}
                  >
                    Learn More
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={() => handleTryDemo(product)}
                  >
                    Try Demo
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Section */}
      <section className="technology-section">
        <div className="container">
          <div className="section-header">
            <h2>Built with Modern Technology</h2>
            <p>Our platform leverages cutting-edge technologies to deliver exceptional performance and reliability</p>
          </div>
          
          <div className="technology-grid">
            {technologies.map((tech, index) => (
              <div key={index} className="tech-card">
                <div className="tech-icon">
                  <span>{tech.icon}</span>
                </div>
                <h4 className="tech-name">{tech.name}</h4>
                <p className="tech-description">{tech.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Transform Your IPO Operations?</h2>
            <p>Join leading financial institutions who trust Bluestock for their IPO management needs</p>
            <div className="cta-actions">
              <button className="btn btn-primary btn-lg">Get Started Today</button>
              <button className="btn btn-outline-light btn-lg">Schedule Consultation</button>
            </div>
          </div>
        </div>
      </section>

      {/* Modal */}
      {showModal && (
        <div
          className="modal-overlay"
          onClick={closeModal}
          style={{
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '1000',
            padding: '20px'
          }}
        >
          <div
            className="modal-content"
            onClick={(e) => e.stopPropagation()}
            style={{
              background: 'white',
              borderRadius: '16px',
              maxWidth: '800px',
              width: '100%',
              maxHeight: '90vh',
              overflowY: 'auto',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
            }}
          >
            <div className="modal-header" style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '24px 32px',
              borderBottom: '1px solid #e5e7eb',
              background: 'white'
            }}>
              <h2 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1a1a1a',
                margin: '0'
              }}>
                {modalType === 'learn-more' ? 'Learn More About' : 'Try Demo'} - {selectedProduct?.title}
              </h2>
              <button
                className="modal-close"
                onClick={closeModal}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '28px',
                  color: '#6b7280',
                  cursor: 'pointer',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  lineHeight: '1',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onMouseOver={(e) => {
                  e.target.style.background = '#f3f4f6';
                  e.target.style.color = '#374151';
                }}
                onMouseOut={(e) => {
                  e.target.style.background = 'none';
                  e.target.style.color = '#6b7280';
                }}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              {modalType === 'learn-more' ? (
                <div className="learn-more-content">
                  <h3 style={{color: 'black', fontSize: '20px', marginBottom: '15px'}}>
                    Product Overview
                  </h3>
                  <p style={{color: '#333', fontSize: '16px', lineHeight: '1.6', marginBottom: '20px'}}>
                    {selectedProduct?.description}
                  </p>

                  <h4 style={{color: 'black', fontSize: '18px', marginBottom: '10px'}}>
                    Key Features
                  </h4>
                  <ul style={{listStyle: 'none', padding: '0', margin: '0 0 20px 0'}}>
                    {selectedProduct?.features?.map((feature, index) => (
                      <li key={index} style={{
                        padding: '8px 0',
                        color: '#333',
                        position: 'relative',
                        paddingLeft: '25px',
                        fontSize: '15px'
                      }}>
                        <span style={{
                          position: 'absolute',
                          left: '0',
                          color: '#22c55e',
                          fontWeight: '600'
                        }}>✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <h4 style={{color: 'black', fontSize: '18px', marginBottom: '10px'}}>
                    Benefits
                  </h4>
                  <ul style={{listStyle: 'none', padding: '0', margin: '0 0 20px 0'}}>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Streamlined workflow and increased efficiency
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Real-time data synchronization
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Advanced security and compliance
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      24/7 customer support
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Scalable architecture for growing businesses
                    </li>
                  </ul>

                  <h4 style={{color: 'black', fontSize: '18px', marginBottom: '10px'}}>
                    Technical Specifications
                  </h4>
                  <ul style={{listStyle: 'none', padding: '0', margin: '0 0 20px 0'}}>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Cloud-based infrastructure
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      RESTful API integration
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Multi-platform compatibility
                    </li>
                    <li style={{padding: '8px 0', color: '#333', position: 'relative', paddingLeft: '25px', fontSize: '15px'}}>
                      <span style={{position: 'absolute', left: '0', color: '#22c55e', fontWeight: '600'}}>✓</span>
                      Enterprise-grade security
                    </li>
                  </ul>
                </div>
              ) : (
                <div className="demo-content">
                  <div className="demo-info">
                    <h3>Experience {selectedProduct?.title}</h3>
                    <p>Get hands-on experience with our {selectedProduct?.title.toLowerCase()} through our interactive demo.</p>

                    <div className="demo-features">
                      <h4>What you'll explore:</h4>
                      <ul>
                        {selectedProduct?.features.map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="demo-form">
                      <h4>Request Demo Access</h4>
                      <form onSubmit={(e) => e.preventDefault()}>
                        <div className="form-group">
                          <input type="text" placeholder="Your Name" required />
                        </div>
                        <div className="form-group">
                          <input type="email" placeholder="Email Address" required />
                        </div>
                        <div className="form-group">
                          <input type="text" placeholder="Company Name" />
                        </div>
                        <div className="form-group">
                          <select required>
                            <option value="">Select Company Size</option>
                            <option value="1-10">1-10 employees</option>
                            <option value="11-50">11-50 employees</option>
                            <option value="51-200">51-200 employees</option>
                            <option value="200+">200+ employees</option>
                          </select>
                        </div>
                        <button type="submit" className="btn btn-primary btn-lg">
                          Start Demo
                        </button>
                      </form>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
