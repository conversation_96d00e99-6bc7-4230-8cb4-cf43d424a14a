import { useState } from 'react';
import './Products.css';

const Products = () => {
  const [products] = useState([
    {
      id: 1,
      title: 'IPO Management Platform',
      description: 'Comprehensive platform for managing IPO applications, tracking status, and analyzing market trends.',
      features: [
        'Real-time IPO tracking',
        'Application management',
        'Market analysis tools',
        'Portfolio tracking'
      ],
      icon: '📊',
      category: 'Platform'
    },
    {
      id: 2,
      title: 'API Integration Suite',
      description: 'Robust API suite for developers to integrate IPO data and functionality into their applications.',
      features: [
        'RESTful APIs',
        'Real-time data feeds',
        'Webhook support',
        'Developer documentation'
      ],
      icon: '🔗',
      category: 'API'
    },
    {
      id: 3,
      title: 'Analytics Dashboard',
      description: 'Advanced analytics and reporting tools for institutional investors and fund managers.',
      features: [
        'Custom dashboards',
        'Performance metrics',
        'Risk analysis',
        'Automated reporting'
      ],
      icon: '📈',
      category: 'Analytics'
    },
    {
      id: 4,
      title: 'Mobile Application',
      description: 'Native mobile app for iOS and Android with full IPO management capabilities.',
      features: [
        'Mobile-first design',
        'Push notifications',
        'Offline capabilities',
        'Biometric security'
      ],
      icon: '📱',
      category: 'Mobile'
    },
    {
      id: 5,
      title: 'Compliance Suite',
      description: 'Comprehensive compliance and regulatory tools for financial institutions.',
      features: [
        'Regulatory reporting',
        'Audit trails',
        'Risk management',
        'Compliance monitoring'
      ],
      icon: '🛡️',
      category: 'Compliance'
    },
    {
      id: 6,
      title: 'White Label Solution',
      description: 'Customizable white-label platform for brokers and financial service providers.',
      features: [
        'Custom branding',
        'Flexible configuration',
        'Multi-tenant support',
        'Dedicated support'
      ],
      icon: '🏷️',
      category: 'Enterprise'
    }
  ]);

  const [technologies] = useState([
    {
      name: 'React & Node.js',
      description: 'Modern web technologies for scalable applications',
      icon: '⚛️'
    },
    {
      name: 'Cloud Infrastructure',
      description: 'AWS/Azure cloud services for reliability and scale',
      icon: '☁️'
    },
    {
      name: 'Real-time Data',
      description: 'WebSocket connections for live market updates',
      icon: '⚡'
    },
    {
      name: 'Security First',
      description: 'Bank-grade security with encryption and compliance',
      icon: '🔒'
    },
    {
      name: 'AI & Machine Learning',
      description: 'Intelligent insights and predictive analytics',
      icon: '🤖'
    },
    {
      name: 'Microservices',
      description: 'Scalable architecture with independent services',
      icon: '🔧'
    }
  ]);

  return (
    <div className="products-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Powerful Products for Modern Finance</h1>
            <p>Discover our comprehensive suite of financial technology solutions designed to streamline IPO management and investment processes.</p>
            <div className="hero-actions">
              <button className="btn btn-primary btn-lg">Explore Products</button>
              <button className="btn btn-outline-primary btn-lg">Request Demo</button>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="products-section">
        <div className="container">
          <div className="section-header">
            <h2>Our Product Suite</h2>
            <p>Comprehensive solutions for every aspect of IPO management and investment</p>
          </div>
          
          <div className="products-grid">
            {products.map((product) => (
              <div key={product.id} className="product-card">
                <div className="product-header">
                  <div className="product-icon">
                    <span>{product.icon}</span>
                  </div>
                  <div className="product-meta">
                    <span className="product-category">{product.category}</span>
                    <h3 className="product-title">{product.title}</h3>
                  </div>
                </div>
                
                <p className="product-description">{product.description}</p>
                
                <div className="product-features">
                  <h4>Key Features</h4>
                  <ul>
                    {product.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
                
                <div className="product-actions">
                  <button className="btn btn-primary">Learn More</button>
                  <button className="btn btn-outline-secondary">Try Demo</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Section */}
      <section className="technology-section">
        <div className="container">
          <div className="section-header">
            <h2>Built with Modern Technology</h2>
            <p>Our platform leverages cutting-edge technologies to deliver exceptional performance and reliability</p>
          </div>
          
          <div className="technology-grid">
            {technologies.map((tech, index) => (
              <div key={index} className="tech-card">
                <div className="tech-icon">
                  <span>{tech.icon}</span>
                </div>
                <h4 className="tech-name">{tech.name}</h4>
                <p className="tech-description">{tech.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Transform Your IPO Operations?</h2>
            <p>Join leading financial institutions who trust Bluestock for their IPO management needs</p>
            <div className="cta-actions">
              <button className="btn btn-primary btn-lg">Get Started Today</button>
              <button className="btn btn-outline-light btn-lg">Schedule Consultation</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Products;
