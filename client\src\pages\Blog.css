/* Blog Page Styles */
.blog-page {
  min-height: 100vh;
  background-color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Breadcrumb */
.breadcrumb-section {
  background-color: #f8fafc;
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb-link {
  color: #6366f1;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: #4f46e5;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #9ca3af;
}

.breadcrumb-current {
  color: #374151;
  font-weight: 500;
}

/* Blog Header */
.blog-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.blog-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.blog-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Category Filter */
.category-filter {
  background-color: #ffffff;
  padding: 24px 0;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.category-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.category-tab {
  background: transparent;
  border: 2px solid #e5e7eb;
  color: #6b7280;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.category-tab:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.category-tab.active {
  background-color: #6366f1;
  border-color: #6366f1;
  color: white;
}

/* Blog Grid */
.blog-grid-section {
  padding: 60px 0;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.blog-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.blog-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.blog-card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  padding: 20px;
}

.placeholder-content h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

.blog-category-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

.blog-card-content {
  padding: 24px;
}

.blog-card-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.blog-title-link {
  color: #1a1a1a;
  text-decoration: none;
  transition: color 0.2s ease;
}

.blog-title-link:hover {
  color: #6366f1;
}

.blog-card-excerpt {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.blog-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 12px;
  color: #9ca3af;
}

.blog-date,
.blog-read-time {
  font-weight: 500;
}

.read-more-btn {
  display: inline-block;
  background: #6366f1;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.read-more-btn:hover {
  background: #5856eb;
  transform: translateY(-1px);
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 60px 0;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
}

.newsletter-content p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0 0 32px 0;
}

.newsletter-form {
  display: flex;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #475569;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(4px);
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.newsletter-btn {
  background: #6366f1;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.newsletter-btn:hover {
  background: #5856eb;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-title {
    font-size: 36px;
  }
  
  .blog-subtitle {
    font-size: 16px;
  }
  
  .blog-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .blog-card-content {
    padding: 20px;
  }
  
  .category-tabs {
    gap: 6px;
  }
  
  .category-tab {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .newsletter-content h2 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .blog-title {
    font-size: 28px;
  }
  
  .blog-grid {
    gap: 16px;
  }
  
  .blog-card-content {
    padding: 16px;
  }
  
  .placeholder-content h3 {
    font-size: 14px;
  }
}
