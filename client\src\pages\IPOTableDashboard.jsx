import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import './IPOTableDashboard.css';

const IPOTableDashboard = () => {
  const { user } = useAuth();
  const [selectedRows, setSelectedRows] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [ipoData] = useState([
    {
      id: 1,
      company: 'Adani Power',
      priceRange: '₹ 325 - 340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '43250.15 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Ongoing',
      actions: ['Update', 'Details/View']
    },
    {
      id: 2,
      company: 'VBL LTD',
      priceRange: '₹ 225 - 340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '13150.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2018-06-10',
      status: 'Upcoming',
      actions: ['Update', 'Details/View']
    },
    {
      id: 3,
      company: 'Tata Motor',
      priceRange: '₹ 1250.99 - 1340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '13450.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2016-06-10',
      status: 'New Listed',
      actions: ['Update', 'Details/View']
    },
    {
      id: 4,
      company: 'HDFC LTD',
      priceRange: '₹ 1345 - 1350',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '43250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-11',
      status: 'Upcoming',
      actions: ['Update', 'Details/View']
    },
    {
      id: 5,
      company: 'Tata Motor',
      priceRange: '₹ 623 - 635',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '8250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Ongoing',
      actions: ['Update', 'Details/View']
    },
    {
      id: 6,
      company: 'VBL LTD',
      priceRange: '₹ 623 - 635',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Upcoming',
      actions: ['Update', 'Details/View']
    },
    {
      id: 7,
      company: 'Tata Motor',
      priceRange: '₹ 6725 - 6835',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1750.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2027-06-10',
      status: 'New Listed',
      actions: ['Update', 'Details/View']
    },
    {
      id: 8,
      company: 'VBL LTD',
      priceRange: '₹ 1825 - 1835',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2022-06-10',
      status: 'Ongoing',
      actions: ['Update', 'Details/View']
    },
    {
      id: 9,
      company: 'Tata Motor',
      priceRange: '₹ 2325 - 2340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2024-06-10',
      status: 'New Listed',
      actions: ['Update', 'Details/View']
    }
  ]);

  const handleRowSelect = (id) => {
    setSelectedRows(prev => 
      prev.includes(id) 
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedRows.length === ipoData.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows(ipoData.map(ipo => ipo.id));
    }
  };

  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case 'ongoing':
        return 'status-ongoing';
      case 'upcoming':
        return 'status-upcoming';
      case 'new listed':
        return 'status-new-listed';
      default:
        return '';
    }
  };

  const handleRegisterIPO = () => {
    console.log('Register IPO clicked');
  };

  if (!user) {
    return (
      <div className="auth-required">
        <h2>Authentication Required</h2>
        <p>Please sign in to access the IPO dashboard.</p>
      </div>
    );
  }

  return (
    <div className="ipo-table-dashboard">
      <div className="container-fluid">
        <div className="row">
          {/* Sidebar */}
          <div className="col-md-3 sidebar">
            <div className="sidebar-header">
              <div className="brand">
                <span className="brand-icon">📊</span>
                <span className="brand-text">Bluestock Fintech</span>
              </div>
            </div>
            
            <nav className="sidebar-nav">
              <div className="nav-section">
                <h6>MENU</h6>
                <a href="/admin-dashboard" className="nav-item">
                  <i className="icon-dashboard"></i>
                  Dashboard
                </a>
                <a href="/manage-ipo" className="nav-item active">
                  <i className="icon-manage"></i>
                  Manage IPO
                </a>
                <a href="/ipo-subscription" className="nav-item">
                  <i className="icon-subscription"></i>
                  IPO Subscription
                </a>
                <a href="/ipo-allotment" className="nav-item">
                  <i className="icon-allotment"></i>
                  IPO Allotment
                </a>
              </div>
              
              <div className="nav-section">
                <h6>OTHERS</h6>
                <a href="/settings" className="nav-item">
                  <i className="icon-settings"></i>
                  Settings
                </a>
                <a href="/api-manager" className="nav-item">
                  <i className="icon-api"></i>
                  API Manager
                </a>
                <a href="/accounts" className="nav-item">
                  <i className="icon-accounts"></i>
                  Accounts
                </a>
                <a href="/help" className="nav-item">
                  <i className="icon-help"></i>
                  Help
                </a>
              </div>
            </nav>
          </div>

          {/* Main Content */}
          <div className="col-md-9 main-content">
            <div className="dashboard-header">
              <div className="header-left">
                <h1>Upcoming IPO | Dashboard</h1>
              </div>
              <div className="header-right">
                <button className="btn btn-primary" onClick={handleRegisterIPO}>
                  Register IPO
                </button>
                <div className="user-info">
                  <span>Hi, {user.name?.split(' ')[0] || 'User'}!</span>
                  <div className="user-avatar">
                    <img src={user.profilePicture || '/default-avatar.png'} alt="User" />
                  </div>
                </div>
              </div>
            </div>

            {/* IPO Table */}
            <div className="table-container">
              <div className="table-wrapper">
                <table className="ipo-table">
                  <thead>
                    <tr>
                      <th>
                        <input
                          type="checkbox"
                          checked={selectedRows.length === ipoData.length}
                          onChange={handleSelectAll}
                        />
                      </th>
                      <th>Company</th>
                      <th>Price Band</th>
                      <th>Open</th>
                      <th>Close</th>
                      <th>ISSUE SIZE</th>
                      <th>ISSUE Type</th>
                      <th>Listing Date</th>
                      <th>Status</th>
                      <th>Action</th>
                      <th>Details/View</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ipoData.map((ipo) => (
                      <tr key={ipo.id} className={selectedRows.includes(ipo.id) ? 'selected' : ''}>
                        <td>
                          <input
                            type="checkbox"
                            checked={selectedRows.includes(ipo.id)}
                            onChange={() => handleRowSelect(ipo.id)}
                          />
                        </td>
                        <td className="company-cell">{ipo.company}</td>
                        <td>{ipo.priceRange}</td>
                        <td>{ipo.open}</td>
                        <td>{ipo.close}</td>
                        <td>{ipo.issueSize}</td>
                        <td>{ipo.issueType}</td>
                        <td>{ipo.listingDate}</td>
                        <td>
                          <span className={`status-badge ${getStatusClass(ipo.status)}`}>
                            {ipo.status}
                          </span>
                        </td>
                        <td>
                          <button className="btn btn-primary btn-sm">
                            Update
                          </button>
                        </td>
                        <td>
                          <div className="action-buttons">
                            <button className="btn btn-outline-primary btn-sm view-btn">
                              👁️
                            </button>
                            <button className="btn btn-outline-secondary btn-sm settings-btn">
                              ⚙️
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="pagination-container">
                <div className="pagination-info">
                  <span>Showing 1 to {ipoData.length} of {ipoData.length} entries</span>
                </div>
                <div className="pagination">
                  <button 
                    className="page-btn"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  >
                    Previous
                  </button>
                  <button className="page-btn active">1</button>
                  <button className="page-btn">2</button>
                  <button className="page-btn">...</button>
                  <button className="page-btn">10</button>
                  <button 
                    className="page-btn"
                    onClick={() => setCurrentPage(prev => prev + 1)}
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPOTableDashboard;
