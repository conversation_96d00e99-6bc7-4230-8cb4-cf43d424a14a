import { useState } from 'react';
import AdminSidebar from '../components/AdminSidebar';
import './IPOTableDashboard.css';

const IPOTableDashboard = () => {
  const [ipoData] = useState([
    {
      id: 1,
      company: 'Adani Power',
      priceBand: '₹ 326 - 340',
      open: '2024-06-03',
      close: '2024-06-05',
      issueSize: '43250.15 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Ongoing',
      logo: '🔋'
    },
    {
      id: 2,
      company: 'VBL LTD',
      priceRange: '₹ 225 - 340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '13150.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2018-06-10',
      status: 'Upcoming',
      actions: ['Update', 'Details/View']
    },
    {
      id: 3,
      company: 'Tata Motor',
      priceRange: '₹ 1250.99 - 1340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '13450.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2016-06-10',
      status: 'New Listed',
      actions: ['Update', 'Details/View']
    },
    {
      id: 4,
      company: 'HDFC LTD',
      priceRange: '₹ 1345 - 1350',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '43250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-11',
      status: 'Upcoming',
      actions: ['Update', 'Details/View']
    },
    {
      id: 5,
      company: 'Tata Motor',
      priceRange: '₹ 623 - 635',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '8250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Ongoing',
      actions: ['Update', 'Details/View']
    },
    {
      id: 6,
      company: 'VBL LTD',
      priceRange: '₹ 623 - 635',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2023-06-10',
      status: 'Upcoming',
      actions: ['Update', 'Details/View']
    },
    {
      id: 7,
      company: 'Tata Motor',
      priceRange: '₹ 6725 - 6835',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1750.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2027-06-10',
      status: 'New Listed',
      actions: ['Update', 'Details/View']
    },
    {
      id: 8,
      company: 'VBL LTD',
      priceRange: '₹ 1825 - 1835',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2022-06-10',
      status: 'Ongoing',
      actions: ['Update', 'Details/View']
    },
    {
      id: 9,
      company: 'Tata Motor',
      priceRange: '₹ 2325 - 2340',
      open: '2024-06-05',
      close: '2024-06-05',
      issueSize: '1250.5 Cr.',
      issueType: 'Book Built',
      listingDate: '2024-06-10',
      status: 'New Listed',
      actions: ['Update', 'Details/View']
    }
  ]);

  const handleRowSelect = (id) => {
    setSelectedRows(prev => 
      prev.includes(id) 
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedRows.length === ipoData.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows(ipoData.map(ipo => ipo.id));
    }
  };

  const getStatusClass = (status) => {
    switch (status.toLowerCase()) {
      case 'ongoing':
        return 'status-ongoing';
      case 'upcoming':
        return 'status-upcoming';
      case 'new listed':
        return 'status-new-listed';
      default:
        return '';
    }
  };

  const handleRegisterIPO = () => {
    console.log('Register IPO clicked');
  };

  const handleUpdate = (id) => {
    console.log('Update IPO:', id);
  };

  const handleView = (id) => {
    console.log('View IPO:', id);
  };

  const handleDelete = (id) => {
    console.log('Delete IPO:', id);
  };

  return (
    <div className="ipo-table-dashboard">
      <AdminSidebar />

      <div className="main-content">
        <div className="page-header">
          <h1>Upcoming IPO | Dashboard</h1>
          <button className="btn-register-ipo">Register IPO</button>
        </div>

        <div className="content-wrapper">
          <div className="table-container">
            <div className="table-header">
              <div className="highlighted-section">
                <div className="table-wrapper">
                  <table className="ipo-table">
                    <thead>
                      <tr>
                        <th>Company</th>
                        <th>Price Band</th>
                        <th>Open</th>
                        <th>Close</th>
                        <th>Issue Size</th>
                        <th>Issue Type</th>
                        <th>Listing Date</th>
                        <th>Status</th>
                        <th>Action</th>
                        <th>Details/View</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ipoData.map((ipo) => (
                        <tr key={ipo.id} className={ipo.id <= 7 ? 'highlighted-row' : ''}>
                          <td>
                            <div className="company-cell">
                              <span className="company-logo">{ipo.logo}</span>
                              <span className="company-name">{ipo.company}</span>
                            </div>
                          </td>
                          <td>{ipo.priceBand}</td>
                          <td>{ipo.open}</td>
                          <td>{ipo.close}</td>
                          <td>{ipo.issueSize}</td>
                          <td>{ipo.issueType}</td>
                          <td>{ipo.listingDate}</td>
                          <td>
                            <span className={`status-badge ${getStatusClass(ipo.status)}`}>
                              {ipo.status}
                            </span>
                          </td>
                          <td>
                            <button
                              className="btn-update"
                              onClick={() => handleUpdate(ipo.id)}
                            >
                              Update
                            </button>
                          </td>
                          <td>
                            <div className="action-buttons">
                              <button
                                className="btn-view"
                                onClick={() => handleView(ipo.id)}
                              >
                                👁️
                              </button>
                              <button
                                className="btn-delete"
                                onClick={() => handleDelete(ipo.id)}
                              >
                                🗑️
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="pagination">
              <div className="pagination-info">
                Showing 1 to 10 of 10 entries
              </div>
              <div className="pagination-controls">
                <button className="page-btn active">1</button>
                <button className="page-btn">2</button>
                <button className="page-btn">...</button>
                <button className="page-btn">10</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPOTableDashboard;
