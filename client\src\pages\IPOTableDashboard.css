/* IPO Table Dashboard Styles */
.ipo-table-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.ipo-table-dashboard .container-fluid {
  padding: 0;
}

/* Sidebar Styles */
.ipo-table-dashboard .sidebar {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 0;
  border-right: 1px solid #e9ecef;
}

.ipo-table-dashboard .sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.ipo-table-dashboard .brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ipo-table-dashboard .brand-icon {
  font-size: 24px;
}

.ipo-table-dashboard .brand-text {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.ipo-table-dashboard .sidebar-nav {
  padding: 20px 0;
}

.ipo-table-dashboard .nav-section {
  margin-bottom: 30px;
}

.ipo-table-dashboard .nav-section h6 {
  color: #6c757d;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 0 0 15px 20px;
  text-transform: uppercase;
}

.ipo-table-dashboard .nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #495057;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.ipo-table-dashboard .nav-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
  text-decoration: none;
}

.ipo-table-dashboard .nav-item.active {
  background-color: #e3f2fd;
  color: #1976d2;
  border-right: 3px solid #1976d2;
}

.ipo-table-dashboard .nav-item i {
  margin-right: 12px;
  width: 16px;
}

/* Main Content */
.ipo-table-dashboard .main-content {
  padding: 30px;
  background-color: #f8f9fa;
}

.ipo-table-dashboard .dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.ipo-table-dashboard .dashboard-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.ipo-table-dashboard .header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.ipo-table-dashboard .user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ipo-table-dashboard .user-info span {
  color: #495057;
  font-size: 14px;
  font-weight: 500;
}

.ipo-table-dashboard .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.ipo-table-dashboard .user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Table Container */
.table-container {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-wrapper {
  overflow-x: auto;
  margin-bottom: 20px;
}

/* IPO Table */
.ipo-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.ipo-table th {
  background-color: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  white-space: nowrap;
}

.ipo-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.ipo-table tr:hover {
  background-color: #f8f9fa;
}

.ipo-table tr.selected {
  background-color: #e3f2fd;
}

.company-cell {
  font-weight: 600;
  color: #212529;
}

/* Status Badges */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-ongoing {
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.status-upcoming {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-new-listed {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  color: white;
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.view-btn, .settings-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.pagination-info {
  color: #6c757d;
  font-size: 14px;
}

.pagination {
  display: flex;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background-color: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background-color: #e9ecef;
}

.page-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Checkboxes */
input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* Auth Required */
.ipo-table-dashboard .auth-required {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px 20px;
}

.ipo-table-dashboard .auth-required h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.ipo-table-dashboard .auth-required p {
  color: #6c757d;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ipo-table-dashboard .main-content {
    padding: 20px 15px;
  }
  
  .ipo-table-dashboard .dashboard-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .ipo-table-dashboard .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .table-container {
    padding: 16px;
  }
  
  .ipo-table th,
  .ipo-table td {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .ipo-table-dashboard .sidebar {
    display: none;
  }
}
