.ipo-table-dashboard {
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  padding: 0;
}

.page-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.btn-register-ipo {
  padding: 10px 20px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-register-ipo:hover {
  background: #1d4ed8;
}

.content-wrapper {
  padding: 32px;
}

.table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.highlighted-section {
  border: 3px solid #fbbf24;
  border-radius: 16px;
  padding: 4px;
  background: #fffbeb;
}

.table-wrapper {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.ipo-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.ipo-table thead {
  background: #f8fafc;
}

.ipo-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ipo-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f3f4f6;
  color: #1f2937;
}

.highlighted-row {
  background: #fef3c7;
}

.company-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-logo {
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.company-name {
  font-weight: 500;
  color: #1f2937;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-ongoing {
  background: #dcfce7;
  color: #166534;
}

.status-upcoming {
  background: #dbeafe;
  color: #1e40af;
}

.status-new-listed {
  background: #fef3c7;
  color: #92400e;
}

.btn-update {
  padding: 6px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-update:hover {
  background: #1d4ed8;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-view,
.btn-delete {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.btn-view:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.btn-delete:hover {
  background: #fef2f2;
  border-color: #fecaca;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  background: white;
  border-top: 1px solid #f3f4f6;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background: #f9fafb;
}

.page-btn.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }

  .content-wrapper {
    padding: 16px;
  }

  .page-header {
    padding: 16px 20px;
  }

  .ipo-table {
    font-size: 12px;
  }

  .ipo-table th,
  .ipo-table td {
    padding: 12px 8px;
  }
}
