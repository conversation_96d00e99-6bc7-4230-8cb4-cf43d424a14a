import { useState } from 'react';
import './IPOHome.css';

const IPOHome = () => {
  const [featuredIPOs] = useState([
    {
      id: 1,
      company: 'Nova Agritech Ltd.',
      logo: '🌾',
      priceRange: 'Rs. 39 - 41',
      open: '2024-01-22',
      close: '2024-01-24',
      issueSize: '143.81 Cr.',
      status: 'Open',
      featured: true
    },
    {
      id: 2,
      company: 'EPACK Durable Ltd.',
      logo: '📦',
      priceRange: 'Rs. 218 - 230',
      open: '2024-01-19',
      close: '2024-01-23',
      issueSize: '640.05 Cr.',
      status: 'Open',
      featured: true
    },
    {
      id: 3,
      company: 'OLA Electric Mobility Ltd.',
      logo: '⚡',
      priceRange: 'Not Issued',
      open: 'Not Issued',
      close: 'Not Issued',
      issueSize: '1800 Cr.',
      status: 'Upcoming',
      featured: true
    }
  ]);

  const [upcomingIPOs] = useState([
    {
      id: 4,
      company: 'RK <PERSON>wamy Ltd.',
      logo: '🎯',
      issueSize: 'Not Issued',
      status: 'Upcoming'
    },
    {
      id: 5,
      company: 'Cromwell Stays Ltd.',
      logo: '🏨',
      issueSize: 'Not Issued',
      status: 'Upcoming'
    },
    {
      id: 6,
      company: 'Imagine marketing Ltd.',
      logo: '💡',
      issueSize: '2000 cr.',
      status: 'Upcoming'
    },
    {
      id: 7,
      company: 'One MobiKwik Systems Ltd.',
      logo: '💳',
      issueSize: '1900 Cr.',
      status: 'Upcoming'
    },
    {
      id: 8,
      company: '16 Travenues Technology',
      logo: '✈️',
      issueSize: '1600 Cr.',
      status: 'Upcoming'
    }
  ]);

  return (
    <div className="ipo-home">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1>Invest in Tomorrow's Leaders Today</h1>
              <p>Discover and invest in the most promising IPOs with Bluestock's comprehensive platform. Get real-time updates, detailed analysis, and seamless application process.</p>
              <div className="hero-actions">
                <button className="btn btn-primary btn-lg">Explore IPOs</button>
                <button className="btn btn-outline-primary btn-lg">Learn More</button>
              </div>
            </div>
            <div className="hero-image">
              <div className="hero-graphic">
                <div className="chart-placeholder">
                  <div className="chart-bars">
                    <div className="bar bar-1"></div>
                    <div className="bar bar-2"></div>
                    <div className="bar bar-3"></div>
                    <div className="bar bar-4"></div>
                    <div className="bar bar-5"></div>
                  </div>
                  <div className="chart-trend">📈</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured IPOs Section */}
      <section className="featured-section">
        <div className="container">
          <div className="section-header">
            <h2>Featured IPOs</h2>
            <p>Don't miss out on these high-potential investment opportunities</p>
          </div>
          
          <div className="featured-grid">
            {featuredIPOs.map((ipo) => (
              <div key={ipo.id} className="featured-card">
                <div className="card-header">
                  <div className="company-info">
                    <div className="company-logo">
                      <span className="logo-icon">{ipo.logo}</span>
                    </div>
                    <div className="company-details">
                      <h3 className="company-name">{ipo.company}</h3>
                      <span className={`status-badge ${ipo.status.toLowerCase()}`}>
                        {ipo.status}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="card-details">
                  {ipo.priceRange !== 'Not Issued' && (
                    <div className="detail-item">
                      <label>Price Range</label>
                      <span>{ipo.priceRange}</span>
                    </div>
                  )}
                  <div className="detail-item">
                    <label>Issue Size</label>
                    <span>{ipo.issueSize}</span>
                  </div>
                  {ipo.open !== 'Not Issued' && (
                    <div className="detail-item">
                      <label>Timeline</label>
                      <span>{ipo.open} - {ipo.close}</span>
                    </div>
                  )}
                </div>

                <div className="card-actions">
                  <button className="btn btn-primary">Apply Now</button>
                  <button className="btn btn-outline-secondary">View Details</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Upcoming IPOs Section */}
      <section className="upcoming-section">
        <div className="container">
          <div className="section-header">
            <h2>Upcoming IPOs</h2>
            <p>Stay ahead with these upcoming investment opportunities</p>
          </div>
          
          <div className="upcoming-grid">
            {upcomingIPOs.map((ipo) => (
              <div key={ipo.id} className="upcoming-card">
                <div className="company-info">
                  <div className="company-logo">
                    <span className="logo-icon">{ipo.logo}</span>
                  </div>
                  <div className="company-details">
                    <h4 className="company-name">{ipo.company}</h4>
                    <span className="issue-size">{ipo.issueSize}</span>
                  </div>
                </div>
                <button className="btn btn-outline-primary btn-sm">Notify Me</button>
              </div>
            ))}
          </div>
          
          <div className="section-footer">
            <button className="btn btn-outline-primary">View All Upcoming IPOs</button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Start Your IPO Journey?</h2>
            <p>Join thousands of investors who trust Bluestock for their IPO investments</p>
            <div className="cta-actions">
              <button className="btn btn-primary btn-lg">Get Started</button>
              <button className="btn btn-outline-light btn-lg">Contact Us</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default IPOHome;
