import { useState } from 'react';
import AdminSidebar from '../components/AdminSidebar';
import './RegisterIPO.css';

const RegisterIPO = () => {
  const [activeTab, setActiveTab] = useState('information');
  const [formData, setFormData] = useState({
    companyName: '',
    open: '',
    close: '',
    issueSize: '',
    issueType: '',
    listingDate: '',
    status: '',
    ipoPrice: '',
    listingPrice: '',
    listingGain: '',
    listingDate2: '',
    cmp: '',
    currentReturn: '',
    rhp: '',
    drhp: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  return (
    <div className="register-ipo-page">
      <AdminSidebar />
      
      <div className="main-content">
        <div className="page-header">
          <h1>Register IPO Details</h1>
          <div className="header-actions">
            <button className="btn-cancel">Cancel</button>
            <button className="btn-register">Register</button>
          </div>
        </div>

        <div className="content-wrapper">
          <div className="form-container">
            <div className="form-header">
              <h2>Upcoming IPO Information</h2>
              <p>Manage your IPO Details</p>
            </div>

            <div className="form-tabs">
              <button 
                className={`tab-btn ${activeTab === 'information' ? 'active' : ''}`}
                onClick={() => setActiveTab('information')}
              >
                <span className="tab-number">1</span>
                IPO Information
              </button>
              <button 
                className={`tab-btn ${activeTab === 'info' ? 'active' : ''}`}
                onClick={() => setActiveTab('info')}
              >
                <span className="tab-number">2</span>
                IPO Info
              </button>
            </div>

            <form onSubmit={handleSubmit} className="ipo-form">
              <div className="form-section">
                <h3>IPO Information</h3>
                <p className="section-subtitle">Enter IPO Details</p>

                <div className="form-row">
                  <div className="form-group">
                    <label>Company Logo</label>
                    <div className="logo-upload">
                      <div className="logo-preview">
                        <div className="logo-placeholder">
                          <span>📊</span>
                        </div>
                        <div className="logo-info">
                          <span className="company-tag">Hot India</span>
                          <span className="company-type">Tech IPO</span>
                        </div>
                      </div>
                      <div className="upload-actions">
                        <button type="button" className="btn-upload">Upload Logo</button>
                        <button type="button" className="btn-delete">Delete</button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Company Name</label>
                    <input
                      type="text"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleInputChange}
                      placeholder="Vodafone Idea"
                    />
                  </div>
                  <div className="form-group">
                    <label>Price Band</label>
                    <input
                      type="text"
                      name="priceBand"
                      placeholder="Not Issued"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Open</label>
                    <input
                      type="text"
                      name="open"
                      value={formData.open}
                      onChange={handleInputChange}
                      placeholder="Not Issued"
                    />
                  </div>
                  <div className="form-group">
                    <label>Close</label>
                    <input
                      type="text"
                      name="close"
                      value={formData.close}
                      onChange={handleInputChange}
                      placeholder="Not Issued"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Issue Size</label>
                    <input
                      type="text"
                      name="issueSize"
                      value={formData.issueSize}
                      onChange={handleInputChange}
                      placeholder="2300 Cr."
                    />
                  </div>
                  <div className="form-group">
                    <label>Issue Type</label>
                    <select
                      name="issueType"
                      value={formData.issueType}
                      onChange={handleInputChange}
                    >
                      <option value="">Select Type</option>
                      <option value="Book Built">Book Built</option>
                      <option value="Fixed Price">Fixed Price</option>
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Listing Date</label>
                    <input
                      type="text"
                      name="listingDate"
                      value={formData.listingDate}
                      onChange={handleInputChange}
                      placeholder="Not Issued"
                    />
                  </div>
                  <div className="form-group">
                    <label>Status</label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="">Select Status</option>
                      <option value="Upcoming">Upcoming</option>
                      <option value="Open">Open</option>
                      <option value="Closed">Closed</option>
                      <option value="Listed">Listed</option>
                    </select>
                  </div>
                </div>
              </div>

              <div className="form-section">
                <h4>NEW LISTED IPO DETAILS (WHEN IPO GET LISTED)</h4>
                
                <div className="form-row">
                  <div className="form-group">
                    <label>IPO PRICE</label>
                    <input
                      type="text"
                      name="ipoPrice"
                      value={formData.ipoPrice}
                      onChange={handleInputChange}
                      placeholder="₹ 363"
                    />
                  </div>
                  <div className="form-group">
                    <label>LISTING PRICE</label>
                    <input
                      type="text"
                      name="listingPrice"
                      value={formData.listingPrice}
                      onChange={handleInputChange}
                      placeholder="₹ 435"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>LISTING GAIN</label>
                    <input
                      type="text"
                      name="listingGain"
                      value={formData.listingGain}
                      onChange={handleInputChange}
                      placeholder="19.28 %"
                    />
                  </div>
                  <div className="form-group">
                    <label>LISTING DATE</label>
                    <input
                      type="date"
                      name="listingDate2"
                      value={formData.listingDate2}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>CMP</label>
                    <input
                      type="text"
                      name="cmp"
                      value={formData.cmp}
                      onChange={handleInputChange}
                      placeholder="₹ 363"
                    />
                  </div>
                  <div className="form-group">
                    <label>CURRENT RETURN</label>
                    <input
                      type="text"
                      name="currentReturn"
                      value={formData.currentReturn}
                      onChange={handleInputChange}
                      placeholder="209 %"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>RHP</label>
                    <input
                      type="text"
                      name="rhp"
                      value={formData.rhp}
                      onChange={handleInputChange}
                      placeholder="Enter RHP PDF Link"
                    />
                  </div>
                  <div className="form-group">
                    <label>DRHP</label>
                    <input
                      type="text"
                      name="drhp"
                      value={formData.drhp}
                      onChange={handleInputChange}
                      placeholder="Enter DRHP PDF Link"
                    />
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterIPO;
