import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import './IPOManagement.css';

const IPOManagement = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('information');
  const [ipoData, setIpoData] = useState({
    companyLogo: null,
    companyName: 'Vodafone Idea',
    priceRange: 'Not Issued',
    open: 'Not Issued',
    close: 'Not Issued',
    issueSize: '2300 Cr.',
    issueType: '',
    listingDate: 'Not Issued',
    status: '',
    ipoPrice: '₹ 383',
    listingPrice: '₹ 435',
    listingGain: '13.58 %',
    listingDate2: '2024-05-30',
    cmp: '₹410',
    currentReturn: '7.05 %',
    rhp: '',
    drhp: ''
  });

  const handleInputChange = (field, value) => {
    setIpoData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setIpoData(prev => ({
          ...prev,
          companyLogo: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRegister = () => {
    console.log('Registering IPO:', ipoData);
    // Add registration logic here
  };

  const handleCancel = () => {
    console.log('Cancelling IPO registration');
    // Add cancel logic here
  };

  if (!user) {
    return (
      <div className="auth-required">
        <h2>Authentication Required</h2>
        <p>Please sign in to access IPO management.</p>
      </div>
    );
  }

  return (
    <div className="ipo-management">
      <div className="container-fluid">
        <div className="row">
          {/* Sidebar */}
          <div className="col-md-3 sidebar">
            <div className="sidebar-header">
              <h5>MENU</h5>
            </div>
            <nav className="sidebar-nav">
              <a href="/dashboard" className="nav-item">
                <i className="icon-dashboard"></i>
                Dashboard
              </a>
              <a href="/manage-ipo" className="nav-item active">
                <i className="icon-manage"></i>
                Manage IPO
              </a>
              <a href="/ipo-subscription" className="nav-item">
                <i className="icon-subscription"></i>
                IPO Subscription
              </a>
              <a href="/ipo-allotment" className="nav-item">
                <i className="icon-allotment"></i>
                IPO Allotment
              </a>
            </nav>
            
            <div className="sidebar-section">
              <h6>OTHERS</h6>
              <a href="/settings" className="nav-item">
                <i className="icon-settings"></i>
                Settings
              </a>
              <a href="/api-manager" className="nav-item">
                <i className="icon-api"></i>
                API Manager
              </a>
              <a href="/accounts" className="nav-item">
                <i className="icon-accounts"></i>
                Accounts
              </a>
              <a href="/help" className="nav-item">
                <i className="icon-help"></i>
                Help
              </a>
            </div>
          </div>

          {/* Main Content */}
          <div className="col-md-9 main-content">
            <div className="content-header">
              <div className="header-left">
                <h2>Upcoming IPO Information</h2>
                <p>Manage your IPO Details</p>
              </div>
              <div className="header-actions">
                <button className="btn btn-primary" onClick={handleRegister}>
                  Register
                </button>
                <button className="btn btn-outline-secondary" onClick={handleCancel}>
                  Cancel
                </button>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="tab-navigation">
              <button 
                className={`tab-btn ${activeTab === 'information' ? 'active' : ''}`}
                onClick={() => setActiveTab('information')}
              >
                <i className="icon-info"></i>
                IPO Information
              </button>
              <button 
                className={`tab-btn ${activeTab === 'info' ? 'active' : ''}`}
                onClick={() => setActiveTab('info')}
              >
                <i className="icon-details"></i>
                IPO Info
              </button>
            </div>

            {/* IPO Information Form */}
            <div className="ipo-form">
              <div className="form-header">
                <h3>IPO Information</h3>
                <p>Enter IPO Details</p>
              </div>

              <div className="form-content">
                {/* Company Logo Section */}
                <div className="logo-section">
                  <label>Company Logo</label>
                  <div className="logo-upload">
                    <div className="logo-preview">
                      {ipoData.companyLogo ? (
                        <img src={ipoData.companyLogo} alt="Company Logo" />
                      ) : (
                        <div className="logo-placeholder">
                          <div className="logo-icon">NSE India</div>
                          <small>Not Listed</small>
                        </div>
                      )}
                    </div>
                    <div className="logo-actions">
                      <label className="btn btn-primary btn-sm">
                        Upload Logo
                        <input 
                          type="file" 
                          accept="image/*" 
                          onChange={handleLogoUpload}
                          style={{ display: 'none' }}
                        />
                      </label>
                      <button className="btn btn-outline-danger btn-sm">
                        Delete
                      </button>
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="form-grid">
                  <div className="form-group">
                    <label>Company Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={ipoData.companyName}
                      onChange={(e) => handleInputChange('companyName', e.target.value)}
                      placeholder="Vodafone Idea"
                    />
                  </div>

                  <div className="form-group">
                    <label>Price Band</label>
                    <input
                      type="text"
                      className="form-control"
                      value={ipoData.priceRange}
                      onChange={(e) => handleInputChange('priceRange', e.target.value)}
                      placeholder="Not Issued"
                    />
                  </div>

                  <div className="form-group">
                    <label>Open</label>
                    <input
                      type="text"
                      className="form-control"
                      value={ipoData.open}
                      onChange={(e) => handleInputChange('open', e.target.value)}
                      placeholder="Not Issued"
                    />
                  </div>

                  <div className="form-group">
                    <label>Close</label>
                    <input
                      type="text"
                      className="form-control"
                      value={ipoData.close}
                      onChange={(e) => handleInputChange('close', e.target.value)}
                      placeholder="Not Issued"
                    />
                  </div>

                  <div className="form-group">
                    <label>Issue Size</label>
                    <input
                      type="text"
                      className="form-control"
                      value={ipoData.issueSize}
                      onChange={(e) => handleInputChange('issueSize', e.target.value)}
                      placeholder="2300 Cr."
                    />
                  </div>

                  <div className="form-group">
                    <label>Issue Type</label>
                    <select
                      className="form-control"
                      value={ipoData.issueType}
                      onChange={(e) => handleInputChange('issueType', e.target.value)}
                    >
                      <option value="">Select Issue Type</option>
                      <option value="Book Built">Book Built</option>
                      <option value="Fixed Price">Fixed Price</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>LISTING DATE</label>
                    <input
                      type="text"
                      className="form-control"
                      value={ipoData.listingDate}
                      onChange={(e) => handleInputChange('listingDate', e.target.value)}
                      placeholder="Not Issued"
                    />
                  </div>

                  <div className="form-group">
                    <label>Status</label>
                    <select
                      className="form-control"
                      value={ipoData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                    >
                      <option value="">Select Status</option>
                      <option value="Upcoming">Upcoming</option>
                      <option value="Open">Open</option>
                      <option value="Closed">Closed</option>
                      <option value="Listed">Listed</option>
                    </select>
                  </div>
                </div>

                {/* New Listed IPO Details Section */}
                <div className="listed-section">
                  <h4>NEW LISTED IPO DETAILS (WHEN IPO GET LISTED)</h4>
                  
                  <div className="form-grid">
                    <div className="form-group">
                      <label>IPO PRICE</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.ipoPrice}
                        onChange={(e) => handleInputChange('ipoPrice', e.target.value)}
                        placeholder="₹ 383"
                      />
                    </div>

                    <div className="form-group">
                      <label>LISTING PRICE</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.listingPrice}
                        onChange={(e) => handleInputChange('listingPrice', e.target.value)}
                        placeholder="₹ 435"
                      />
                    </div>

                    <div className="form-group">
                      <label>LISTING GAIN</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.listingGain}
                        onChange={(e) => handleInputChange('listingGain', e.target.value)}
                        placeholder="13.58 %"
                      />
                    </div>

                    <div className="form-group">
                      <label>LISTING DATE</label>
                      <input
                        type="date"
                        className="form-control"
                        value={ipoData.listingDate2}
                        onChange={(e) => handleInputChange('listingDate2', e.target.value)}
                      />
                    </div>

                    <div className="form-group">
                      <label>CMP</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.cmp}
                        onChange={(e) => handleInputChange('cmp', e.target.value)}
                        placeholder="₹410"
                      />
                    </div>

                    <div className="form-group">
                      <label>CURRENT RETURN</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.currentReturn}
                        onChange={(e) => handleInputChange('currentReturn', e.target.value)}
                        placeholder="7.05 %"
                      />
                    </div>

                    <div className="form-group">
                      <label>RHP</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.rhp}
                        onChange={(e) => handleInputChange('rhp', e.target.value)}
                        placeholder="Enter RHP PDF Link"
                      />
                    </div>

                    <div className="form-group">
                      <label>DRHP</label>
                      <input
                        type="text"
                        className="form-control"
                        value={ipoData.drhp}
                        onChange={(e) => handleInputChange('drhp', e.target.value)}
                        placeholder="Enter DRHP PDF Link"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPOManagement;
