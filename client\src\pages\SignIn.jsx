import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './SignIn.css';

const SignIn = () => {
  const { handleGoogleLogin } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [keepSignedIn, setKeepSignedIn] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle email/password login here
    console.log('Sign in with:', formData);
  };

  return (
    <div className="auth-container">
      <div className="auth-card signin-card">
        {/* Bluestock Logo */}
        <div className="auth-logo">
          <div className="logo-icon">
            <div className="logo-bars">
              <div className="bar bar-1"></div>
              <div className="bar bar-2"></div>
              <div className="bar bar-3"></div>
            </div>
          </div>
          <span className="logo-text">BLUESTOCK</span>
        </div>

        <h2 className="auth-title">Sign In</h2>

        {/* Sign In Form */}
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              required
            />
            <a href="#" className="forgot-password">Forgot Password?</a>
          </div>

          <div className="form-options">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={keepSignedIn}
                onChange={(e) => setKeepSignedIn(e.target.checked)}
              />
              <span className="checkmark"></span>
              Keep me signed in
            </label>
          </div>

          <button type="submit" className="auth-btn primary-btn">
            Sign In
          </button>
        </form>

        {/* Divider */}
        <div className="auth-divider">
          <span>or sign in with</span>
        </div>

        {/* Google Sign In */}
        <button onClick={handleGoogleLogin} className="auth-btn google-btn">
          <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" />
          Continue with Google
        </button>

        {/* Create Account Link */}
        <div className="auth-footer">
          <Link to="/signup" className="create-account-link">
            Create an account
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SignIn;