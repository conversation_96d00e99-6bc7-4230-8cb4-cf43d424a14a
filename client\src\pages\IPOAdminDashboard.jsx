import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import './IPOAdminDashboard.css';

const IPOAdminDashboard = () => {
  const { user } = useAuth();
  const [dashboardData] = useState({
    totalIPOs: 30,
    activeIPOs: 9,
    upcomingIPOs: 20,
    allotmentPercentage: 15,
    quickLinks: [
      { name: 'NSE (NSE)', status: 'Visit Now', icon: '🏢' },
      { name: 'BSE (BSE)', status: 'Visit Now', icon: '📈' },
      { name: 'SEBI', status: 'Visit Now', icon: '🏛️' },
      { name: 'Money Control', status: 'Visit Now', icon: '💰' }
    ],
    recentActivity: [
      { company: 'Tata Motors', date: '01 Jan, 2024', status: 'Upcoming' },
      { company: 'Reliance', date: '15 Dec, 2023', status: 'New Listed' },
      { company: 'HDFC Bank', date: '20 Nov, 2023', status: 'Ongoing' }
    ]
  });

  if (!user) {
    return (
      <div className="auth-required">
        <h2>Authentication Required</h2>
        <p>Please sign in to access the admin dashboard.</p>
      </div>
    );
  }

  return (
    <div className="ipo-admin-dashboard">
      <div className="main-content">
        <div className="container-fluid">
          {/* Main Content */}
          <div className="dashboard-content">
            <div className="dashboard-header">
              <div className="header-left">
                <h1>Dashboard</h1>
              </div>
              <div className="header-right">
                <div className="user-info">
                  <span>Hi, {user.name?.split(' ')[0] || 'User'}!</span>
                  <div className="user-avatar">
                    <img src={user.profilePicture || '/default-avatar.png'} alt="User" />
                  </div>
                </div>
              </div>
            </div>

            <div className="dashboard-content">
              <div className="row">
                {/* IPO Dashboard Info */}
                <div className="col-md-4">
                  <div className="info-card">
                    <h3>IPO Dashboard Info</h3>
                    <div className="chart-container">
                      <div className="donut-chart">
                        <div className="chart-center">
                          <span className="chart-number">{dashboardData.totalIPOs}</span>
                          <span className="chart-label">Total IPOs</span>
                        </div>
                        <svg className="chart-svg" viewBox="0 0 200 200">
                          <circle
                            cx="100"
                            cy="100"
                            r="80"
                            fill="none"
                            stroke="#e9ecef"
                            strokeWidth="20"
                          />
                          <circle
                            cx="100"
                            cy="100"
                            r="80"
                            fill="none"
                            stroke="#007bff"
                            strokeWidth="20"
                            strokeDasharray={`${(dashboardData.activeIPOs / dashboardData.totalIPOs) * 502} 502`}
                            strokeDashoffset="125.5"
                            transform="rotate(-90 100 100)"
                          />
                          <circle
                            cx="100"
                            cy="100"
                            r="60"
                            fill="none"
                            stroke="#28a745"
                            strokeWidth="20"
                            strokeDasharray={`${(dashboardData.upcomingIPOs / dashboardData.totalIPOs) * 377} 377`}
                            strokeDashoffset="94.25"
                            transform="rotate(-90 100 100)"
                          />
                        </svg>
                      </div>
                      <div className="chart-legend">
                        <div className="legend-item">
                          <span className="legend-color active"></span>
                          <span>{dashboardData.activeIPOs} Active</span>
                        </div>
                        <div className="legend-item">
                          <span className="legend-color upcoming"></span>
                          <span>{dashboardData.upcomingIPOs} Upcoming</span>
                        </div>
                      </div>
                    </div>
                    <p className="info-subtitle">₹ 20 Cr in Total</p>
                  </div>
                </div>

                {/* Quick Links */}
                <div className="col-md-4">
                  <div className="info-card">
                    <h3>Quick Links</h3>
                    <div className="quick-links">
                      {dashboardData.quickLinks.map((link, index) => (
                        <div key={index} className="quick-link-item">
                          <div className="link-info">
                            <span className="link-icon">{link.icon}</span>
                            <span className="link-name">{link.name}</span>
                          </div>
                          <button className="link-action">{link.status}</button>
                        </div>
                      ))}
                    </div>
                    <p className="info-subtitle">Accessing link, see all document connect</p>
                  </div>
                </div>

                {/* Main Board IPO */}
                <div className="col-md-4">
                  <div className="info-card">
                    <div className="card-header">
                      <h3>Main Board IPO</h3>
                      <button className="view-report-btn">View Report</button>
                    </div>
                    <div className="board-chart">
                      <div className="percentage-display">
                        <span className="percentage">{dashboardData.allotmentPercentage}</span>
                        <span className="percentage-label">Allotment</span>
                      </div>
                      <div className="chart-info">
                        <div className="chart-stats">
                          <div className="stat-item">
                            <span className="stat-number">4</span>
                            <span className="stat-label">Upcoming</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-number">5</span>
                            <span className="stat-label">New Listed</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-number">6</span>
                            <span className="stat-label">Ongoing</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="row mt-4">
                <div className="col-12">
                  <div className="activity-card">
                    <div className="activity-header">
                      <h3>Recent IPO Activity</h3>
                      <button className="view-all-btn">View All</button>
                    </div>
                    <div className="activity-list">
                      {dashboardData.recentActivity.map((activity, index) => (
                        <div key={index} className="activity-item">
                          <div className="activity-info">
                            <span className="activity-company">{activity.company}</span>
                            <span className="activity-date">{activity.date}</span>
                          </div>
                          <span className={`activity-status ${activity.status.toLowerCase().replace(' ', '-')}`}>
                            {activity.status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPOAdminDashboard;
