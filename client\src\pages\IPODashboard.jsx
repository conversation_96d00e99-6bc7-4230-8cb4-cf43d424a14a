import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import './IPODashboard.css';

const IPODashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('information');
  const [ipoData, setIpoData] = useState({
    companyName: 'Vodafone Idea',
    priceRange: 'Not Issued',
    open: 'Not Issued',
    close: 'Not Issued',
    issueSize: '2300 Cr.',
    issueType: '',
    listingDate: 'Not Issued',
    status: '',
    ipoPrice: '₹ 383',
    listingPrice: '₹ 435',
    listingGain: '13.58 %',
    listingDate2: '2024-05-30',
    cmp: '₹410',
    currentReturn: '7.05 %',
    rhp: '',
    drhp: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setIpoData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('IPO Data:', ipoData);
  };

  return (
    <div className="ipo-dashboard">
      {/* Sidebar */}
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <div className="user-profile">
            <div className="profile-avatar">
              <img 
                src={user?.profilePicture || 'https://via.placeholder.com/40?text=BF'} 
                alt="Profile" 
              />
            </div>
            <div className="profile-info">
              <h3>Bluestock Fintech</h3>
              <p>Hi, {user?.name?.split(' ')[0] || 'Vishal'}!</p>
            </div>
            <div className="profile-actions">
              <button className="notification-btn">🔔</button>
              <button className="settings-btn">⚙️</button>
            </div>
          </div>
        </div>

        <nav className="sidebar-nav">
          <div className="nav-section">
            <h4>MENU</h4>
            <ul>
              <li><a href="#" className="nav-link">📊 Dashboard</a></li>
              <li><a href="#" className="nav-link active">📈 Manage IPO</a></li>
              <li><a href="#" className="nav-link">📋 IPO Subscription</a></li>
              <li><a href="#" className="nav-link">📊 IPO Allotment</a></li>
            </ul>
          </div>

          <div className="nav-section">
            <h4>OTHERS</h4>
            <ul>
              <li><a href="#" className="nav-link">⚙️ Settings</a></li>
              <li><a href="#" className="nav-link">👤 My Manager</a></li>
              <li><a href="#" className="nav-link">👥 Accounts</a></li>
              <li><a href="#" className="nav-link">❓ Help</a></li>
            </ul>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="dashboard-main">
        <div className="main-header">
          <div className="header-left">
            <h1>Upcoming IPO Information</h1>
            <p>Manage your IPO Details</p>
          </div>
          <div className="header-actions">
            <button className="btn btn-outline">Cancel</button>
            <button className="btn btn-primary">Register</button>
          </div>
        </div>

        <div className="content-tabs">
          <div className="tab-nav">
            <button 
              className={`tab-btn ${activeTab === 'information' ? 'active' : ''}`}
              onClick={() => setActiveTab('information')}
            >
              📋 IPO Information
            </button>
            <button 
              className={`tab-btn ${activeTab === 'info' ? 'active' : ''}`}
              onClick={() => setActiveTab('info')}
            >
              ℹ️ IPO Info
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'information' && (
              <div className="ipo-form-section">
                <h2>IPO Information</h2>
                <p>Enter IPO Details</p>

                <form onSubmit={handleSubmit} className="ipo-form">
                  <div className="form-row">
                    <div className="form-group">
                      <label>Company Logo</label>
                      <div className="logo-upload">
                        <div className="logo-preview">
                          <div className="logo-placeholder">🏢</div>
                          <span>NSE India</span>
                          <small>Not Listed</small>
                        </div>
                        <div className="upload-actions">
                          <button type="button" className="btn btn-outline">Upload Logo</button>
                          <button type="button" className="btn btn-text">Delete</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="companyName">Company Name</label>
                      <input
                        type="text"
                        id="companyName"
                        name="companyName"
                        value={ipoData.companyName}
                        onChange={handleInputChange}
                        placeholder="Vodafone Idea"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="priceRange">Price Band</label>
                      <input
                        type="text"
                        id="priceRange"
                        name="priceRange"
                        value={ipoData.priceRange}
                        onChange={handleInputChange}
                        placeholder="Not Issued"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="open">Open</label>
                      <input
                        type="text"
                        id="open"
                        name="open"
                        value={ipoData.open}
                        onChange={handleInputChange}
                        placeholder="Not Issued"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="close">Close</label>
                      <input
                        type="text"
                        id="close"
                        name="close"
                        value={ipoData.close}
                        onChange={handleInputChange}
                        placeholder="Not Issued"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="issueSize">Issue Size</label>
                      <input
                        type="text"
                        id="issueSize"
                        name="issueSize"
                        value={ipoData.issueSize}
                        onChange={handleInputChange}
                        placeholder="2300 Cr."
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="issueType">Issue Type</label>
                      <select
                        id="issueType"
                        name="issueType"
                        value={ipoData.issueType}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Issue Type</option>
                        <option value="fresh">Fresh Issue</option>
                        <option value="offer">Offer for Sale</option>
                        <option value="both">Both</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="listingDate">LISTING DATE</label>
                      <input
                        type="text"
                        id="listingDate"
                        name="listingDate"
                        value={ipoData.listingDate}
                        onChange={handleInputChange}
                        placeholder="Not Issued"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="status">Status</label>
                      <select
                        id="status"
                        name="status"
                        value={ipoData.status}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Status</option>
                        <option value="upcoming">Upcoming</option>
                        <option value="open">Open</option>
                        <option value="closed">Closed</option>
                        <option value="listed">Listed</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-section">
                    <h3>NEW LISTED IPO DETAILS (WHEN IPO GET LISTED)</h3>
                    
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="ipoPrice">IPO PRICE</label>
                        <input
                          type="text"
                          id="ipoPrice"
                          name="ipoPrice"
                          value={ipoData.ipoPrice}
                          onChange={handleInputChange}
                          placeholder="₹ 383"
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="listingPrice">LISTING PRICE</label>
                        <input
                          type="text"
                          id="listingPrice"
                          name="listingPrice"
                          value={ipoData.listingPrice}
                          onChange={handleInputChange}
                          placeholder="₹ 435"
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="listingGain">LISTING GAIN</label>
                        <input
                          type="text"
                          id="listingGain"
                          name="listingGain"
                          value={ipoData.listingGain}
                          onChange={handleInputChange}
                          placeholder="13.58 %"
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="listingDate2">LISTING DATE</label>
                        <input
                          type="date"
                          id="listingDate2"
                          name="listingDate2"
                          value={ipoData.listingDate2}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="cmp">CMP</label>
                        <input
                          type="text"
                          id="cmp"
                          name="cmp"
                          value={ipoData.cmp}
                          onChange={handleInputChange}
                          placeholder="₹410"
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="currentReturn">CURRENT RETURN</label>
                        <input
                          type="text"
                          id="currentReturn"
                          name="currentReturn"
                          value={ipoData.currentReturn}
                          onChange={handleInputChange}
                          placeholder="7.05 %"
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="rhp">RHP</label>
                        <input
                          type="text"
                          id="rhp"
                          name="rhp"
                          value={ipoData.rhp}
                          onChange={handleInputChange}
                          placeholder="Enter RHP PDF Link"
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="drhp">DRHP</label>
                        <input
                          type="text"
                          id="drhp"
                          name="drhp"
                          value={ipoData.drhp}
                          onChange={handleInputChange}
                          placeholder="Enter DRHP PDF Link"
                        />
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPODashboard;
