import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './SignUp.css';

const SignUp = () => {
  const { handleGoogleLogin } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: ''
  });
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!agreeToTerms) {
      alert('Please agree to the terms of service');
      return;
    }
    // Handle email/password signup here
    console.log('Sign up with:', formData);
  };

  return (
    <div className="auth-container">
      <div className="auth-card signup-card">
        {/* Bluestock Logo */}
        <div className="auth-logo">
          <div className="logo-icon">
            <div className="logo-bars">
              <div className="bar bar-1"></div>
              <div className="bar bar-2"></div>
              <div className="bar bar-3"></div>
            </div>
          </div>
          <span className="logo-text">BLUESTOCK</span>
        </div>

        <h2 className="auth-title">Create an account</h2>

        {/* Sign Up Form */}
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              placeholder="Shivika Verma"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Enter your email address"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-options">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={agreeToTerms}
                onChange={(e) => setAgreeToTerms(e.target.checked)}
              />
              <span className="checkmark"></span>
              By continuing, you agree to our <a href="#" className="terms-link">terms of service</a>
            </label>
          </div>

          <button type="submit" className="auth-btn primary-btn">
            Sign up
          </button>
        </form>

        {/* Divider */}
        <div className="auth-divider">
          <span>or sign up with</span>
        </div>

        {/* Google Sign Up */}
        <button onClick={handleGoogleLogin} className="auth-btn google-btn">
          <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" />
          Continue with Google
        </button>

        {/* Sign In Link */}
        <div className="auth-footer">
          <span>Already have an account? </span>
          <Link to="/signin" className="signin-link">
            Sign in here
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SignUp;