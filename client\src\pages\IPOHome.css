/* IPO Home Page Styles */
.ipo-home {
  min-height: 100vh;
  background-color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text h1 {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 24px 0;
}

.hero-text p {
  font-size: 18px;
  line-height: 1.6;
  margin: 0 0 32px 0;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-lg {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-primary {
  background-color: #ffffff;
  color: #667eea;
  border-color: #ffffff;
}

.btn-primary:hover {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
  transform: translateY(-2px);
}

.btn-outline-primary {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
}

.btn-outline-primary:hover {
  background-color: #ffffff;
  color: #667eea;
  transform: translateY(-2px);
}

.btn-outline-light {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
}

.btn-outline-light:hover {
  background-color: #ffffff;
  color: #667eea;
  transform: translateY(-2px);
}

/* Hero Graphic */
.hero-graphic {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.chart-placeholder {
  position: relative;
  width: 300px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 40px;
  backdrop-filter: blur(10px);
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 120px;
  gap: 8px;
}

.bar {
  background: linear-gradient(to top, #ffffff, rgba(255, 255, 255, 0.7));
  border-radius: 4px;
  flex: 1;
  animation: growBar 2s ease-out;
}

.bar-1 { height: 60%; }
.bar-2 { height: 80%; }
.bar-3 { height: 100%; }
.bar-4 { height: 70%; }
.bar-5 { height: 90%; }

.chart-trend {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
}

@keyframes growBar {
  from { height: 0; }
  to { height: var(--final-height); }
}

/* Section Styles */
.featured-section,
.upcoming-section {
  padding: 80px 0;
}

.upcoming-section {
  background-color: #f8fafc;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.section-header p {
  font-size: 18px;
  color: #666666;
  margin: 0;
}

/* Featured Grid */
.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.featured-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.featured-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.card-header {
  margin-bottom: 20px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.company-logo {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-icon {
  font-size: 24px;
}

.company-details {
  flex: 1;
}

.company-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.open {
  background-color: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-badge.upcoming {
  background-color: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

.card-details {
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.card-actions .btn {
  flex: 1;
  padding: 10px 16px;
  font-size: 14px;
}

.btn-outline-secondary {
  background-color: transparent;
  color: #6b7280;
  border-color: #d1d5db;
}

.btn-outline-secondary:hover {
  background-color: #6b7280;
  color: #ffffff;
}

/* Upcoming Grid */
.upcoming-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.upcoming-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.upcoming-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.upcoming-card .company-info {
  gap: 12px;
}

.upcoming-card .company-logo {
  width: 50px;
  height: 50px;
}

.upcoming-card .logo-icon {
  font-size: 20px;
}

.upcoming-card .company-name {
  font-size: 16px;
  margin: 0 0 4px 0;
}

.issue-size {
  font-size: 12px;
  color: #6b7280;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
}

.section-footer {
  text-align: center;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #1e293b 0%, #**********%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.cta-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.cta-content p {
  font-size: 18px;
  margin: 0 0 32px 0;
  opacity: 0.9;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .hero-text h1 {
    font-size: 36px;
  }
  
  .hero-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .chart-placeholder {
    width: 250px;
    height: 150px;
    padding: 30px;
  }
  
  .featured-grid {
    grid-template-columns: 1fr;
  }
  
  .upcoming-grid {
    grid-template-columns: 1fr;
  }
  
  .upcoming-card {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}
