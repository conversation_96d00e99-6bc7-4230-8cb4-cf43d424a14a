/* Footer.css - Bluestock Footer */
.main-footer {
  background: #1f2937;
  color: #d1d5db;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Footer Content */
.footer-content {
  padding: 48px 0 32px 0;
}

.footer-columns {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 32px;
}

.footer-column h3 {
  font-size: 16px;
  font-weight: 600;
  color: #f9fafb;
  margin: 0 0 16px 0;
}

.footer-column ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.footer-column li {
  margin-bottom: 8px;
}

.footer-column a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.footer-column a:hover {
  color: #f3f4f6;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid #374151;
  padding: 32px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

/* Social Media */
.social-media {
  display: flex;
  gap: 16px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #374151;
  border-radius: 8px;
  color: #9ca3af;
  transition: all 0.2s ease;
}

.social-link:hover {
  background: #4b5563;
  color: #f3f4f6;
}

/* Company Info */
.company-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.company-logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.company-logo .logo-icon {
  width: 28px;
  height: 28px;
}

.company-logo .logo-bars {
  display: flex;
  gap: 2px;
  height: 100%;
  align-items: flex-end;
}

.company-logo .bar {
  width: 5px;
  background: #6366f1;
  border-radius: 1px;
}

.company-logo .bar-1 {
  height: 60%;
}

.company-logo .bar-2 {
  height: 100%;
}

.company-logo .bar-3 {
  height: 80%;
}

.company-logo .logo-text {
  font-size: 16px;
  font-weight: 700;
  color: #f9fafb;
  letter-spacing: 0.5px;
}

.company-details {
  text-align: center;
}

.company-details p {
  margin: 4px 0;
  font-size: 12px;
  color: #9ca3af;
}

/* Startup Logo */
.startup-logo {
  display: flex;
  align-items: center;
}

.startup-text {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.startup-hash {
  color: #10b981;
  margin-right: 2px;
}

.startup-main {
  color: #f59e0b;
}

/* Disclaimer */
.footer-disclaimer {
  margin-bottom: 24px;
}

.footer-disclaimer p {
  font-size: 12px;
  line-height: 1.6;
  color: #9ca3af;
  margin: 12px 0;
}

.footer-disclaimer a {
  color: #6366f1;
  text-decoration: none;
}

.footer-disclaimer a:hover {
  text-decoration: underline;
}

/* Copyright */
.footer-copyright {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #374151;
}

.footer-copyright p {
  font-size: 12px;
  color: #6b7280;
  margin: 4px 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-columns {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 24px;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .footer-columns {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .footer-content {
    padding: 32px 0 24px 0;
  }
  
  .footer-bottom {
    padding: 24px 0;
  }
}

@media (max-width: 480px) {
  .footer-columns {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .footer-container {
    padding: 0 16px;
  }
  
  .social-media {
    justify-content: center;
  }
}
